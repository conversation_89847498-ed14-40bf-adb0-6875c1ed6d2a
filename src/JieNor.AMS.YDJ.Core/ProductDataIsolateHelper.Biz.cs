using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using JieNor.Framework.Interface.Log;
using ServiceStack;
using System.Diagnostics;
using System.Runtime.Serialization;
using System.Text;
using JieNor.AMS.YDJ.Core.Utils;
using JieNor.Framework.Interface.Cache;
using System.Data;

namespace JieNor.AMS.YDJ.Core
{


    /// <summary>
    /// 【商品】数据权限隔离辅助类：授权的业务逻辑
    /// </summary>  
    public partial class ProductDataIsolateHelper
    {



        /// <summary>
        /// 销售组织与业绩品牌映射关系
        /// </summary>
        private static List<SeriesSalOrgInfo> SalOrgSeriesMapInfo
        {
            get
            {
                return _currCache._salOrgSeriesMapInfo;
            }
        }

        private static List<AgentInfo> AgentInfo
        {
            get
            {
                return _currCache._agentInfo;
            }
        }

        private static List<DeliverSerilInfo> DeliverSerilInfo
        {
            get
            {
                return _currCache._deliverSerilInfo;
            }
        }
        private static List<SeriesBrandMapInfo> SerilBrandMapInfo
        {
            get
            {
                return _currCache._serilBrandMapInfo;
            }
        }

        private static List<AgentProductAuthInfor> ProductAuthInfo
        {
            get
            {
                return _currCache._productAuthInfo;
            }
        }

        private static Dictionary<string, Dictionary<string, ProductSalOrgInfo>> SerieProducntInfo
        {
            get
            {
                return _currCache._serieProducntInfo;
            }
        }

        /// <summary>
        /// 按创建组织分的组织商品列表，  key--创建组织id，value商品列表
        /// </summary>
        private static Dictionary<string, Dictionary<string, ProductSalOrgInfo>> MainOrgProducntInfo
        {
            get
            {
                return _currCache._mainOrgProducntInfo;
            }
        }


        /// <summary>
        /// 按销售组织分的组织商品列表 key--销售组织id，value可售商品列表
        /// </summary>
        private static Dictionary<string, Dictionary<string, ProductSalOrgInfo>> SalOrgProducntInfo
        {
            get
            {
                return _currCache._salOrgProducntInfo;
            }
        }


        /// <summary>
        /// 
        /// </summary>
        private static HashSet<ProductSalOrgInfo> ProductSalOrgInfo
        {
            get
            {
                return _currCache._productSalOrgInfo;
            }
        }


        /// <summary>
        /// 
        /// </summary>
        private static List<IGrouping<GroupPrdSalOrgInfo, ProductSalOrgInfo>> PrdInfos
        {
            get
            {
                return _currCache._prdInfos;
            }
        }




        private HashSet<string> GetAutSeriesIds(UserContext ctx, DataQueryRuleParaInfo rulePara, List<UserAgentInfo> orgInfos)
        {
            var retAutSeriesIds = new HashSet<string>();
            retAutSeriesIds.Add("#####");//避免没有数据时不会产生临时表，导致授权错误
            if (orgInfos.Any())
            {
                //TODO 用户是经销商或门店的，可以看到的系列信息 = 总部授权系列 + 自己建的系列
                TryCacheDatas(ctx);

                var prdAuths = new List<AgentProductAuthInfor>();

                //门店看到的系列=给经销商授权的系列，
                prdAuths = ProductAuthInfo.Where(f =>
                {
                    return orgInfos.Any(x => x.OrgId == f.forgid);
                }).ToList();

                HashSet<string> brandIds, seriIds, excludeProdIds;
                ParseBrandAndSerieInfo(prdAuths, out brandIds, out seriIds, out excludeProdIds);

                foreach (var seriId in seriIds)
                {
                    retAutSeriesIds.Add(seriId);
                }

                //系列授权+自建系列
                var prdIds = GetSerieInfo(ctx, orgInfos, brandIds, seriIds);
                foreach (var seriId in prdIds)
                {
                    retAutSeriesIds.Add(seriId);
                }

                //按商品授权部分对应的系列信息
                var includeProdIds = GetIncludeProdSeriesIds(prdAuths);
                foreach (var seriId in includeProdIds)
                {
                    retAutSeriesIds.Add(seriId);
                }

                ReleaseProductAuth(prdAuths);
            }

            return retAutSeriesIds;
        }




        /// <summary>
        /// 大客户订单，只能选择特定系列商品
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <param name="autPrdInfo"></param> 
        /// <returns></returns>
        private List<ProductSalOrgInfo> GetVipCustomerFilterProduct(UserContext ctx, List<UserAgentInfo> orgInfos, DataQueryRuleParaInfo rulePara,
                                                    List<ProductSalOrgInfo> autPrdInfo)
        {
            if (!IsVipCustomerSO(ctx, rulePara))
            {
                return autPrdInfo;
            }

            //var result = new List<ProductSalOrgInfo>();
            //List<string> canSeri = GetVipCustomerSeri(ctx, rulePara);
            //foreach (var seriNo in canSeri)
            //{
            //    result.AddRange(autPrdInfo.Where(f => f.SeriesNo.EqualsIgnoreCase(seriNo) || f.AuxSeriesNo.Contains(seriNo, StringComparer.OrdinalIgnoreCase)));
            //}

            ////大客户订单，允许选自建商品
            //foreach (var item in orgInfos)
            //{
            //    result.AddRange(autPrdInfo.Where(f => f.MainorgId.EqualsIgnoreCase(item.OrgId)));
            //}

            //return result;

            //2022-07-15 任务33851 需求变更：大客户销售订单下单时，不做限制和控制，经销商代理品牌的产品均可下单

            return autPrdInfo;
        }


        /// <summary>
        /// 获取大客户可选的系列
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <returns>系列编码</returns>
        private List<string> GetVipCustomerSeri(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            var result = new List<string>();
            //1、【品牌】编码为”C”的商品
            var brandSeri = SerilBrandMapInfo.Where(f => f.BrandNo.EqualsIgnoreCase("C")).ToList();
            result.AddRange(brandSeri.Select(f => f.SeriesNo));
            //2、【系列】的编码为” K1” (慕思儿童)的商品
            result.Add("K1");
            //3、【系列】的编码为” M1” (慕思助眠)(M1)的商品
            result.Add("M1");
            //4、【系列】的编码为” Z1” (通配品牌)的商品
            result.Add("Z1");

            return result;
        }


        /// <summary>
        /// 排除： 商品授权清单中设置了不允许采购的商品
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <param name="orgInfos"></param>
        /// <param name="autPrdInfo"></param>
        /// <param name="prdAuths"></param>
        /// <returns></returns>
        private static List<ProductSalOrgInfo> GetCanPurFilterProduct(UserContext ctx, DataQueryRuleParaInfo rulePara,
                                                                    List<ProductSalOrgInfo> autPrdInfo, List<AgentProductAuthInfor> prdAuths)
        {
            var exceptPrd = new List<ProductSalOrgInfo>();
            //采购订单、采购入库单中不允许选择【不允许采购】的商品
            if (rulePara.SrcFormId.EqualsIgnoreCase("ydj_purchaseorder") || rulePara.SrcFormId.EqualsIgnoreCase("stk_postockin"))
            {

                var products = prdAuths.SelectMany(f => f.fproductauthlist).ToList();
                var grp = products.GroupBy(f => f.fproductid).ToList();
                foreach (var item in grp)
                {
                    var fproductid = item.Key;
                    if (fproductid.IsNullOrEmptyOrWhiteSpace())
                    {
                        continue;
                    }

                    //任意一个商品授权清单中定义了允许采购，则允许选择改商品
                    var canpruchase = item.ToList().Any(f => !f.fnopurchase);
                    if (canpruchase)
                    {
                        continue;
                    }

                    if (rulePara.SrcPara != null
                        && rulePara.SrcPara.ContainsKey("deliverid") 
                        && !rulePara.SrcPara["deliverid"].IsNullOrEmptyOrWhiteSpace()
                        && (IsBillType(rulePara, "ydj_purchaseorder", "BHDD_SYS_01", "备货订单")|| IsBillType(rulePara, "ydj_purchaseorder", "BTCGDD_SYS_01", "标准采购"))) {
                        string cityid = null;
                        var deliverId = rulePara.SrcPara["deliverid"];
                        var deliverSeril = DeliverSerilInfo.Where(f => f.DeliveryId == deliverId)?.FirstOrDefault();
                        cityid = deliverSeril?.CityId ?? "";
                        var prdAuthsList = prdAuths.Where(p => p.fcityid == cityid).FirstOrDefault()?.fproductauthlist?.ToList();
                        if (prdAuthsList != null && !prdAuthsList.Any(f => f.fproductid == fproductid))
                        {
                            continue;
                        }

                    }

                    var exp = autPrdInfo.Where(f => f.MatId == fproductid).ToList();
                    exceptPrd.AddRange(exp);

                    ReleaseProductSalOrg(exp);
                }
                grp?.Clear();
                grp = null;

                products?.Clear();
                products = null;
            }

            var result = autPrdInfo.Except(exceptPrd).ToList();

            ReleaseProductSalOrg(exceptPrd);

            return result;
        }


      
        /// <summary>
        /// 第三层过滤：【系列】的编码为”Z2” 时 (慕思经典-新渠道)是否可见
        /// #75244 【250491】 【慕思现场4.10-4.14】新渠道系列拆分Z2 慕思经典-甄选 ，Z5 慕思经典-优选
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <param name="orgInfos"></param>
        /// <param name="autPrdInfo"></param> 
        /// <returns></returns>
        private static List<ProductSalOrgInfo> GetThirdFilterProduct(UserContext ctx, DataQueryRuleParaInfo rulePara, List<UserAgentInfo> orgInfos,
                                                                    List<ProductSalOrgInfo> autPrdInfo)
        {
            var sql = $@"/*dialect*/  SELECT fnumber FROM t_ydj_series with(nolock) WHERE fisnewchannel=1";
            var seriesfnumber = ctx.ExecuteDynamicObject(sql, new List<SqlParam>() { })?.Select(p =>Convert.ToString(p["fnumber"])).ToList();
            var result = autPrdInfo;
            var expSeries = autPrdInfo
                .Where(f => seriesfnumber.Contains(f.SeriesNo) || (f.AuxSeriesNo != null && f.AuxSeriesNo.Any(num => seriesfnumber.Contains(num)))).ToList();
            if (!CanSelectSpecalSeril(ctx, rulePara))
            {
                result = autPrdInfo.Except(expSeries).ToList();
            }

            ReleaseProductSalOrg(expSeries);

            return result;
        }


        /// <summary>
        /// 是否可以选择特定系列：编码为”Z2”的【系列】(慕思经典-新渠道)
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <returns></returns>
        private static bool CanSelectSpecalSeril(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            //销售合同：大客户订单允许允许选择  编码为”Z2”的【系列】(慕思经典-新渠道)，其他不允许
            //销售出库不允许选择  编码为”Z2”的【系列】(慕思经典-新渠道)
            //采购订单：非摆场订单允许选择  编码为”Z2”的【系列】(慕思经典-新渠道)，其他不允许
            //43055 摆场订单 无法选择Z2新渠道逻辑去除，希望摆场也可以选择Z2
            if ((rulePara.SrcFormId.EqualsIgnoreCase("ydj_order") && !IsVipCustomerSO(ctx, rulePara) && !IsStoreSampleSO(ctx, rulePara)) || rulePara.SrcFormId.EqualsIgnoreCase("stk_sostockout"))
            {
                return false;
            }

            return true;
        }


        /// <summary>
        /// 第二层过滤：按商品销售组织进行过滤，分两种情况 ：
        ///1、按 【销售组织与业绩品牌关系】里面定义的销售组织跟商品上定义的销售组织进行匹配过滤
        ///2、按业务单据上的供应商所对应的销售组织跟商品上定义的销售组织进行匹配过滤
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <param name="orgInfos">经销商组织信息</param>
        /// <param name="autPrdIds">从商品授权清单中得到的可见的商品id</param>
        /// <returns></returns>
        private List<ProductSalOrgInfo> GetSecondFilterProduct(UserContext ctx, DataQueryRuleParaInfo rulePara, List<UserAgentInfo> orgInfos,
                                                            List<ProductSalOrgInfo> autPrdIds)
        {
            List<ProductSalOrgInfo> result = new List<ProductSalOrgInfo>();

            //业务单据上有供应商字段的，按业务单据上的供应商所对应的销售组织跟商品上定义的销售组织进行匹配过滤
            if (rulePara.SrcFormId.EqualsIgnoreCase("ydj_purchaseprice") || rulePara.SrcFormId.EqualsIgnoreCase("stk_otherstockin")
                || rulePara.SrcFormId.EqualsIgnoreCase("stk_postockin") || rulePara.SrcFormId.EqualsIgnoreCase("aft_repairorder"))
            {
                if (rulePara.SrcPara != null && rulePara.SrcPara.ContainsKey("supplierid"))
                {
                    var salOrgIds = GetSupplierSalOrgInfo(ctx, rulePara);
                    var prdIds = GetAuthProductInfoByOrg(ctx, rulePara, autPrdIds, salOrgIds);
                    result.AddRange(prdIds);

                    salOrgIds?.Clear();
                    salOrgIds = null;

                    ReleaseProductSalOrg(prdIds);
                }
            }
            //业务单据上无供应商字段的，按 【销售组织与业绩品牌关系】里面定义的销售组织跟商品上定义的销售组织进行匹配过滤
            //else if (rulePara.SrcFormId.EqualsIgnoreCase("ydj_order") || rulePara.SrcFormId.EqualsIgnoreCase("stk_sostockout")
            //    || rulePara.SrcFormId.EqualsIgnoreCase("ydj_price") || rulePara.SrcFormId.EqualsIgnoreCase("stk_otherstockout")
            //    || rulePara.SrcFormId.EqualsIgnoreCase("stk_inventorytransfer") || rulePara.SrcFormId.EqualsIgnoreCase("stk_initstockbill"))             
            else
            {
                var allSeriIds = GetAutSeriesIds(ctx, rulePara, orgInfos);//商品授权清单中涉及的系列id

                //1、根据商品授权清单的系列，匹配【送达方】中的系列，返回可见的系列
                var deliverySeril = GetDeliverySerilInfo(ctx, rulePara, allSeriIds);
                //2、通过1中得到的系列，匹配【商品】上定义的系列，来确定改商品是否可见
                var bySeriOrgMap = GetAuthProductInfo(ctx, rulePara, autPrdIds, deliverySeril);

                if (rulePara.SrcFormId.EqualsIgnoreCase("ydj_order"))
                {
                    //根据商品授权清单的系列，匹配【销售组织与业绩品牌关系】中的系列，得到系列对应的销售组织
                    List<SeriesSalOrgInfo> seriSalOrgIdInfo = GetSerilSalOrgInfo(ctx, rulePara, allSeriIds);
                    //剔除已经禁用的商品
                    bySeriOrgMap = ExceptForbidProductInfo(ctx, rulePara, bySeriOrgMap, seriSalOrgIdInfo);

                    seriSalOrgIdInfo?.Clear();
                    seriSalOrgIdInfo = null;
                }

                result.AddRange(bySeriOrgMap);

                allSeriIds?.Clear();
                allSeriIds = null;

                deliverySeril?.Clear();
                deliverySeril = null;

                ReleaseProductSalOrg(bySeriOrgMap);
            }

            return result;
        }


        /// <summary>
        /// 根据商品授权清单的系列，匹配【销售组织与业绩品牌关系】中的系列，返回商品系列对应的销售组织
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <param name="allSeriIds">商品授权清单中涉及的系列id</param>
        /// <returns>系列对应的销售组织信息</returns>
        private List<SeriesSalOrgInfo> GetSerilSalOrgInfo(UserContext ctx, DataQueryRuleParaInfo rulePara, HashSet<string> allSeriIds)
        {
            var result = new List<SeriesSalOrgInfo>();
            foreach (var seriId in allSeriIds)
            {
                var ex = SalOrgSeriesMapInfo.Where(f => f.SeriesId == seriId)?.ToList();
                result.AddRange(ex);

                ex?.Clear();
            }
            return result;
        }


        /// <summary>
        /// 根据商品授权清单的系列，匹配【送达方】中的系列，返回可见的系列
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <param name="allSeriIds">商品授权清单中涉及的系列id</param>
        /// <returns>可见的商品系列</returns>
        private List<DeliverSerilInfo> GetDeliverySerilInfo(UserContext ctx, DataQueryRuleParaInfo rulePara, HashSet<string> allSeriIds)
        {
            var result = new List<DeliverSerilInfo>();
            if ("ydj_purchaseorder".EqualsIgnoreCase(rulePara.SrcFormId) && rulePara.SrcPara != null && rulePara.SrcPara.ContainsKey("deliverid"))
            {
                var deliverId = rulePara.SrcPara["deliverid"];
                var deliverSeril = DeliverSerilInfo.Where(f => f.DeliveryId == deliverId)?.ToList();
                foreach (var seriId in allSeriIds)
                {
                    var ex = deliverSeril.FirstOrDefault(f => f.SeriesId == seriId);
                    if (ex != null)
                    {
                        result.Add(ex);
                    }
                }

                deliverSeril?.Clear();
                deliverSeril = null;
            }

            return result;
        }


        /// <summary>
        /// 通过【供应商的销售组织】与【商品上的销售组织】匹配，返回可见的商品id
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <param name="autPrdIds">从商品授权清单中得到的可见的商品id</param>
        /// <param name="seriSalOrgIdInfo">供应商对应的销售组织</param>
        /// <returns></returns>
        private List<ProductSalOrgInfo> GetAuthProductInfoByOrg(UserContext ctx, DataQueryRuleParaInfo rulePara, List<ProductSalOrgInfo> autPrdIds, List<BaseDataSummary> salOrgIds)
        {
            var result = new List<ProductSalOrgInfo>();

            //自建商品 
            var mainOrgPrd = MainOrgProducntInfo;
            var canSelectMyself = CanSelectMyself(ctx, rulePara);
            if (canSelectMyself && mainOrgPrd.ContainsKey(ctx.Company))
            {
                var mySelf = mainOrgPrd[ctx.Company];
                result = (from x in autPrdIds
                          join y in mySelf on x.MatId equals y.Key
                          select y.Value).ToList();
            }

            if (salOrgIds == null || salOrgIds.Count == 0)
            {
                return result;
            }

            // 如果是二级分销商用户，这里直接返回商品授权清单中授权的商品，无需按照送达方销售公司过滤
            if (ctx.IsSecondOrg)
            {
                result.AddRange(autPrdIds);
                return result;
            }

            var prdIds = new HashSet<ProductSalOrgInfo>(autPrdIds);
            var orgIds = new HashSet<string>(salOrgIds.Select(f => f.Id));

            var salOrgPrds = SalOrgProducntInfo;
            foreach (var orgPrd in salOrgPrds)
            {
                if (!orgIds.Contains(orgPrd.Key))
                {
                    continue;
                }

                var query = (from x in prdIds
                             join y in orgPrd.Value on x.MatId equals y.Key
                             select x).ToList();
                result.AddRange(query);

                ReleaseProductSalOrg(query);
            }

            prdIds.Clear();
            prdIds = null;
            orgIds.Clear();
            orgIds = null;

            return result;
        }


        /// <summary>
        /// 是否可以选择自建商品
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <returns></returns>
        private static bool CanSelectMyself(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            //摆场订单，不可选择自建商品
            if (IsBaiChangPO(ctx, rulePara))
            {
                return false;
            }

            //大客户订单，可选择自建商品
            if (IsVipCustomerSO(ctx, rulePara))
            {
                return true;
            }

            return true;
        }




        /// <summary>
        /// 通过【商品系列对应的销售组织】与【商品上的销售组织】匹配，剔除掉已经禁用的商品
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <param name="autPrdInfo">从商品授权清单中得到的可见的商品信息</param> 
        /// <returns></returns>
        private List<ProductSalOrgInfo> ExceptForbidProductInfo(UserContext ctx, DataQueryRuleParaInfo rulePara, List<ProductSalOrgInfo> autPrdInfo, List<SeriesSalOrgInfo> seriSalOrgIdInfo)
        {
            var result = new List<ProductSalOrgInfo>(autPrdInfo);

            var allSalesOrgIds = new HashSet<string>(seriSalOrgIdInfo.Select(f => f.SalOrgId));
            var seriGrp = seriSalOrgIdInfo.GroupBy(f => f.SeriesId).Select(f => new { SeriesId = f.Key, Orgs = f.ToList().Select(x => x.SalOrgId) }).ToList();
            var matGrp = ProductSalOrgInfo.GroupBy(f => f.MatId).ToList();//商品的销售组织信息，按商品分组

            var query = (from x in autPrdInfo
                         join y in matGrp on x.MatId equals y.Key
                         select new
                         {
                             x = x,
                             y = y,
                             allSaleOrgIds = y.ToList().Select(f => f.SalOrgId).ToList(),
                             canSaleOrgIds = y.ToList().Where(f => f.SalOrgEnable == true).Select(f => f.SalOrgId).ToList()
                         }
                         ).ToList();

            var beRemove = new List<ProductSalOrgInfo>();
            foreach (var item in query)
            {
                //自建商品
                if (item.x.MainorgId == ctx.Company)
                {
                    continue;
                }
                if (item.allSaleOrgIds.All(f => f.IsNullOrEmptyOrWhiteSpace()))
                {
                    //商品上没有定义销售组织的，认为所有组织可销售
                    continue;
                }
                if (item.canSaleOrgIds.Any(f => allSalesOrgIds.Contains(f)))
                {
                    continue;
                }

                beRemove.Add(item.x);
            }

            if (beRemove.Count > 0)
            {
                result = result.Except(beRemove).ToList();

                ReleaseProductSalOrg(beRemove);
            }

            allSalesOrgIds?.Clear();
            seriGrp?.Clear();
            matGrp?.Clear();
            query?.Clear();
            allSalesOrgIds = null;
            seriGrp = null;
            matGrp = null;
            query?.Clear();
            query = null;

            return result;
        }

        /// <summary>
        /// 通过【送达方】的产品系列与【商品上的系列】匹配，返回可见的商品id
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <param name="autPrdIds">从商品授权清单中得到的可见的商品id</param>
        /// <param name="deliverySerilIds">送达方可见的商品系列</param>
        /// <returns></returns>
        private List<ProductSalOrgInfo> GetAuthProductInfo(UserContext ctx, DataQueryRuleParaInfo rulePara, List<ProductSalOrgInfo> autPrdIds,
                                                        List<DeliverSerilInfo> deliverySerilIds)
        {
            var result = new List<ProductSalOrgInfo>();

            //自建商品 
            var mainOrgPrd = MainOrgProducntInfo;
            var canSelectMyself = CanSelectMyself(ctx, rulePara);
            if (canSelectMyself && mainOrgPrd.ContainsKey(ctx.Company))
            {
                var mySelf = mainOrgPrd[ctx.Company];
                result = (from x in autPrdIds
                          join y in mySelf on x.MatId equals y.Key
                          select y.Value).ToList();
            }

            var prdIds = new List<ProductSalOrgInfo>(autPrdIds);
            if (rulePara.SrcFormId.EqualsIgnoreCase("ydj_purchaseorder"))
            {
                //采购订单的，需要按送达方过滤
                var resultX = GetAuthProductInfoByPO(ctx, rulePara, deliverySerilIds, prdIds);
                result.AddRange(resultX);

                ReleaseProductSalOrg(resultX);
            }
            else
            {
                //非采购订单的，不需要按送达方过滤，都可见
                result.AddRange(autPrdIds);
            }

            ReleaseProductSalOrg(prdIds);

            return result;
        }

        private List<ProductSalOrgInfo> GetAuthProductInfoByPO(UserContext ctx, DataQueryRuleParaInfo rulePara, List<DeliverSerilInfo> deliverySerilIds, List<ProductSalOrgInfo> prdIds)
        {
            List<ProductSalOrgInfo> result = new List<ProductSalOrgInfo>();

            var seriIds = deliverySerilIds.Where(f => f.IsEnable)?.Select(f => f.SeriesId)?.Distinct()?.ToList();
            if (seriIds == null || seriIds.Count == 0)
            {
                return result;
            }

            var serieProducntInfo = SerieProducntInfo;
            var hashPrdIds = new HashSet<ProductSalOrgInfo>(prdIds);
            foreach (var seriePrd in serieProducntInfo)
            {
                var exist = seriIds.Any(x => seriePrd.Key.IndexOf(x) > -1);
                if (!exist)
                {
                    continue;
                }

                foreach (var item in hashPrdIds)
                {
                    if (seriePrd.Value.ContainsKey(item.MatId))
                    {
                        var xx = seriePrd.Value[item.MatId];

                        result.Add(xx);
                    }
                }
            }

            //【供应商的销售组织】与【商品上的销售组织】匹配，返回可见的商品id
            if (rulePara.SrcPara != null && rulePara.SrcPara.ContainsKey("supplierid"))
            {
                var salOrgIds = GetSupplierSalOrgInfo(ctx, rulePara);
                result = GetAuthProductInfoByOrg(ctx, rulePara, result, salOrgIds);

                salOrgIds.Clear();
            }

            seriIds?.Clear();
            seriIds = null;
            hashPrdIds.Clear();

            return result;
        }



        /// <summary>
        /// 第一层过滤：按商品授权清单获取对应的商品id
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <param name="orgInfos"></param>
        /// <returns></returns>
        private static List<ProductSalOrgInfo> GetFirstFilterProduct(UserContext ctx, DataQueryRuleParaInfo rulePara,
                                                        List<UserAgentInfo> orgInfos, List<AgentProductAuthInfor> prdAuths)
        {
            List<ProductSalOrgInfo> autPrdIds = new List<ProductSalOrgInfo>();

            HashSet<string> brandIds, seriIds, excludeProdIds;

            ParseBrandAndSerieInfo(prdAuths, out brandIds, out seriIds, out excludeProdIds);

            var sql = $@"/*dialect*/ 
            SELECT
            	org.fid,age.fnumber,age.fcustomchannel 
            FROM
            	t_bas_agent age
            	JOIN t_bas_organization org ON org.fnumber in ( {string.Join(",", orgInfos.Select(p => $"'{p.OrgNo}'"))})
            WHERE
            	age.fnumber = org.fnumber";

            var agents = ctx.ExecuteDynamicObject(sql, new List<SqlParam>() { });
            //当前经销商是大客户才走大客户商品授权清单，如果当前经销商不是大客户，而同城市同实控人存在其他经销商是大客户，还是走原有的商品授权清单逻辑。
            var bigAgents = agents.Where(p => Convert.ToString(p["fcustomchannel"]) == "1" && Convert.ToString(p["fid"]).EqualsIgnoreCase(ctx.Company));

            if (agents.Count > 0 && bigAgents.Count() > 0)
            {
                //按商品授权部分（这部分数据是通过 new 出来的，本身保证了唯一性）
                List<ProductSalOrgInfo> includeProdIds = GetIncludeProdIds(ctx, rulePara, prdAuths);
                autPrdIds.AddRange(includeProdIds);

                //去掉排除在外的商品（性能优化）
                var _autPrdIds = new List<ProductSalOrgInfo>();
                foreach (var item in autPrdIds)
                {
                    _autPrdIds.Add(item);
                }

                brandIds?.Clear();
                seriIds?.Clear();
                excludeProdIds?.Clear();
                ReleaseProductSalOrg(autPrdIds);
                ReleaseProductSalOrg(includeProdIds);

                return _autPrdIds;

                ////品牌授权+系列授权
                //var prdIds = GetBrandAndSerieInfoNew(ctx, orgInfos, brandIds, seriIds);
                //autPrdIds.AddRange(prdIds);

                ////按商品授权部分（这部分数据是通过 new 出来的，本身保证了唯一性）
                //List<ProductSalOrgInfo> includeProdIds = GetIncludeProdIds(ctx, rulePara, prdAuths);
                //autPrdIds.AddRange(includeProdIds);

                //var _autPrdIds = new List<ProductSalOrgInfo>();
                //foreach (var item in autPrdIds)
                //{
                //    var agent = bigAgents.FirstOrDefault(p => Convert.ToString(p["fid"]) == item.MainorgId);
                //    if (!agent.IsNullOrEmptyOrWhiteSpace() && Convert.ToString(agent["fcustomchannel"]) == "1")
                //    {
                //        var prdAuth = prdAuths.FirstOrDefault(p => p.forgid == item.MainorgId);

                //        if (!prdAuth.IsNullOrEmptyOrWhiteSpace())
                //        {
                //            var fproAuthList = prdAuth.fproductauthlist.FirstOrDefault(p => p.fproductid == item.MatId);
                //            if (!fproAuthList.IsNullOrEmptyOrWhiteSpace())
                //            {
                //                _autPrdIds.Add(item);
                //            }
                //        }
                //        else
                //        {
                //            if (!excludeProdIds.Contains(item.MatId))
                //            {
                //                _autPrdIds.Add(item);
                //            }
                //        }
                //    }
                //    //else
                //    //{
                //    //    if (!excludeProdIds.Contains(item.MatId))
                //    //    {
                //    //        _autPrdIds.Add(item);
                //    //    }
                //    //}
                //}

                ////prdIds?.Clear();
                //brandIds?.Clear();
                //seriIds?.Clear();
                //excludeProdIds?.Clear();
                //ReleaseProductSalOrg(autPrdIds);
                //ReleaseProductSalOrg(includeProdIds);

                //return _autPrdIds;
            }
            else
            {
                //品牌授权+系列授权
                var prdIds = GetBrandAndSerieInfoNew(ctx, orgInfos, brandIds, seriIds);
                autPrdIds.AddRange(prdIds);

                //按商品授权部分（这部分数据是通过 new 出来的，本身保证了唯一性）
                List<ProductSalOrgInfo> includeProdIds = GetIncludeProdIds(ctx, rulePara, prdAuths);
                autPrdIds.AddRange(includeProdIds);

                //去掉排除在外的商品（性能优化）
                var _autPrdIds = new List<ProductSalOrgInfo>();
                foreach (var item in autPrdIds)
                {
                    if (!excludeProdIds.Contains(item.MatId))
                    {
                        _autPrdIds.Add(item);
                    }
                }

                prdIds?.Clear();
                brandIds?.Clear();
                seriIds?.Clear();
                excludeProdIds?.Clear();
                ReleaseProductSalOrg(autPrdIds);
                ReleaseProductSalOrg(includeProdIds);

                return _autPrdIds;

            }
        }



        private string GetStoreProxyType(UserContext ctx, DataQueryRuleParaInfo rulePara, out string storeOrgId)
        {
            var deptId = (rulePara.SrcPara != null && rulePara.SrcPara.ContainsKey("deptId")) ? rulePara.SrcPara["deptId"] : "";
            var orgId = GetStoreOrgIdByDept(ctx, deptId);
            storeOrgId = orgId;

            var serieNos = GetStoreProxySerieNo(ctx, orgId);
            if (serieNos.Any())
            {
                //仅代理”慕思助眠”
                var byM1 = serieNos.Where(f => f.EqualsIgnoreCase("Z1") || f.EqualsIgnoreCase("Z1"))?.ToList();
                var result = serieNos.Except(byM1)?.ToList();
                byM1?.Clear();
                if (result != null && !result.Any(f => f.IsNullOrEmptyOrWhiteSpace() == false))
                {
                    result?.Clear();
                    return "byM1";
                }

                //仅代理”Home”
                var byA1 = serieNos.Where(f => f.EqualsIgnoreCase("A1") || f.EqualsIgnoreCase("Z1") || f.EqualsIgnoreCase("M1"))?.ToList();
                result = serieNos.Except(byA1)?.ToList();
                byA1?.Clear();
                if (result != null && !result.Any(f => f.IsNullOrEmptyOrWhiteSpace() == false))
                {
                    result?.Clear();
                    return "byA1";
                }

                //仅代理”慕思美居”
                var byY1 = serieNos.Where(f => f.EqualsIgnoreCase("Y1") || f.EqualsIgnoreCase("Z1") || f.EqualsIgnoreCase("M1"))?.ToList();
                result = serieNos.Except(byY1)?.ToList();
                byY1?.Clear();
                if (result != null && !result.Any(f => f.IsNullOrEmptyOrWhiteSpace() == false))
                {
                    result?.Clear();
                    return "byY1";
                }
            }

            serieNos?.Clear();
            serieNos = null;

            return "";
        }


        /// <summary>
        /// 获取门店代理的系列编码信息
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="storeOrgId"></param>
        /// <returns></returns>
        private List<string> GetStoreProxySerieNo(UserContext ctx, string storeOrgId)
        {
            var serieNos = new List<string>();
            var prdAuths = ProductAuthInfo.Where(f => storeOrgId.EqualsIgnoreCase(f.forgid))?.ToList();
            if (prdAuths != null && prdAuths.Any())
            {
                var brandSeries = prdAuths.SelectMany(f => f.fproductauthbs).ToList();
                foreach (var item in brandSeries)
                {
                    if (item.fserieid.IsNullOrEmptyOrWhiteSpace())
                    {
                        continue;
                    }

                    var _seriIds = item.fserieid.SplitKey();
                    foreach (var id in _seriIds)
                    {
                        var serMap = SerilBrandMapInfo.FirstOrDefault(f => id.EqualsIgnoreCase(f.SeriesId));
                        var serieNo = serMap?.SeriesNo;
                        if (!serieNo.IsNullOrEmptyOrWhiteSpace())
                        {
                            serieNos.Add(serieNo);
                        }
                    }

                }

                ReleaseProductAuth(prdAuths);
                brandSeries?.Clear();
                brandSeries = null;
            }

            return serieNos;
        }


        private static bool IsBillType(DataQueryRuleParaInfo rulePara,string formId,string billtypeNo,string billTypeName) {
            return rulePara.SrcFormId.EqualsIgnoreCase(formId) && (
                   (rulePara.SrcPara.ContainsKey("billtypeNo") && rulePara.SrcPara["billtypeNo"].EqualsIgnoreCase(billtypeNo))
                 ||(rulePara.SrcPara.ContainsKey("billtypeName") && rulePara.SrcPara["billtypeName"].EqualsIgnoreCase(billTypeName)));


        }
        /// <summary>
        /// 是否摆场类型的采购订单
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <returns></returns>
        private static bool IsPurchaseOrder(UserContext ctx, DataQueryRuleParaInfo rulePara, out string storeOrgId)
        {
            storeOrgId = "";
            var isBaiChang = IsBaiChangPO(ctx, rulePara);

            //通过部门id查找到对应的门店组织id
            if (isBaiChang)
            {
                var deptId = rulePara.SrcPara.ContainsKey("deptId") ? rulePara.SrcPara["deptId"] : "";
                storeOrgId = GetStoreOrgIdByDept(ctx, deptId);
            }

            return isBaiChang;
        }


        /// <summary>
        /// 是否摆场订单
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <returns></returns>
        private static bool IsBaiChangPO(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            if (!(rulePara.SrcFormId.EqualsIgnoreCase("pur_reqorder") || rulePara.SrcFormId.EqualsIgnoreCase("ydj_purchaseorder")))
            {
                return false;
            }

            if (rulePara.SrcPara == null)
            {
                return false;
            }

            return (rulePara.SrcPara.ContainsKey("billtypeNo") && rulePara.SrcPara["billtypeNo"].EqualsIgnoreCase("BCDD_SYS_01"))
                || (rulePara.SrcPara.ContainsKey("billtypeName") && rulePara.SrcPara["billtypeName"].EqualsIgnoreCase("摆场订单"));
        }


        /// <summary>
        /// 是否大客户订单
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <returns></returns>
        private static bool IsVipCustomerSO(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            if (!rulePara.SrcFormId.EqualsIgnoreCase("ydj_order"))
            {
                return false;
            }

            if (rulePara.SrcPara == null)
            {
                return false;
            }

            return (rulePara.SrcPara.ContainsKey("billtypeNo") && rulePara.SrcPara["billtypeNo"].EqualsIgnoreCase("DKHXSHT_SYS_01"))
                || (rulePara.SrcPara.ContainsKey("billtypeName") && rulePara.SrcPara["billtypeName"].EqualsIgnoreCase("大客户销售合同"));
        }
        /// <summary>
        /// 是否是门店上样订单
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rulePara"></param>
        /// <returns></returns>
        private static bool IsStoreSampleSO(UserContext ctx, DataQueryRuleParaInfo rulePara)
        {
            if (!rulePara.SrcFormId.EqualsIgnoreCase("ydj_order"))
            {
                return false;
            }

            if (rulePara.SrcPara == null)
            {
                return false;
            }

            return (rulePara.SrcPara.ContainsKey("billtypeNo") && rulePara.SrcPara["billtypeNo"].EqualsIgnoreCase("MDSYXSHT_SYS_01"))
                || (rulePara.SrcPara.ContainsKey("billtypeName") && rulePara.SrcPara["billtypeName"].EqualsIgnoreCase("门店上样"));
        }

        /// <summary>
        /// 获取按品牌\系列授权的商品id
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="brandIds"></param>
        /// <param name="seriIds"></param>
        /// <returns></returns>
        private static HashSet<ProductSalOrgInfo> GetBrandAndSerieInfoNew(UserContext ctx, List<UserAgentInfo> orgInfos, HashSet<string> brandIds, HashSet<string> seriIds)
        {
            var result = new List<ProductSalOrgInfo>();

            //按品牌授权
            if (brandIds.Count > 0)
            {
                var xx = PrdInfos.Where(f => brandIds.Contains(f.Key.BrandId));
                if (xx != null)
                {
                    result.AddRange(xx.SelectMany(f => f));
                }
            }

            //按系列授权
            if (seriIds.Count > 0)
            {
                //注意商品上有两个字段【系列】【附属品牌】
                var xx = PrdInfos.Where(f => seriIds.Contains(f.Key.SeriesId)).ToList();
                if (xx != null)
                {
                    result.AddRange(xx.SelectMany(f => f));
                }

                foreach (var item in seriIds)
                {
                    xx = PrdInfos.Where(f => f.Key.AuxSeriesid.Contains(item)).ToList();
                    if (xx != null)
                    {
                        result.AddRange(xx.SelectMany(f => f));
                    }
                }

                xx?.Clear();
            }

            return new HashSet<ProductSalOrgInfo>(result);
        }




        /// <summary>
        /// 获取按系列授权的商品id及自建的系列id
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="brandIds"></param>
        /// <param name="seriIds"></param>
        /// <returns></returns>
        private List<string> GetSerieInfo(UserContext ctx, List<UserAgentInfo> orgInfos, HashSet<string> brandIds, HashSet<string> seriIds)
        {
            var result = new List<string>();
            result.AddRange(seriIds);

            //品牌对应的系列
            if (brandIds.Count > 0)
            {
                foreach (var item in brandIds)
                {
                    result.AddRange(SerilBrandMapInfo.Where(f => f.BrandId == item)?.Select(f => f.SeriesId));
                }
            }

            //自建的系列
            if (orgInfos.Count > 0)
            {
                foreach (var item in orgInfos)
                {
                    result.AddRange(SerilBrandMapInfo.Where(f => f.MainOrgId == item.OrgId)?.Select(f => f.SeriesId));
                }
            }

            return result;
        }



        /// <summary>
        /// 获取按品牌、系列授权的相关信息
        /// </summary>
        /// <param name="prdAuths"></param>
        /// <param name="brandIds"></param>
        /// <param name="seriIds"></param>
        /// <param name="excludeProdIds"></param>
        private static void ParseBrandAndSerieInfo(List<AgentProductAuthInfor> prdAuths, out HashSet<string> brandIds, out HashSet<string> seriIds, out HashSet<string> excludeProdIds)
        {
            brandIds = new HashSet<string>();
            seriIds = new HashSet<string>();
            excludeProdIds = new HashSet<string>();
            var brandSeries = prdAuths.SelectMany(f => f.fproductauthbs).ToList();
            foreach (var item in brandSeries)
            {
                var fbrandid = item.fbrandid;
                var fserieid = item.fserieid;
                if (fbrandid.IsNullOrEmptyOrWhiteSpace())
                {
                    if (!fserieid.IsNullOrEmptyOrWhiteSpace())
                    {
                        var _seriIds = fserieid.SplitKey();
                        foreach (var seriId in _seriIds)
                        {
                            seriIds.Add(seriId);
                        }
                    }
                }
                else
                {
                    if (fserieid.IsNullOrEmptyOrWhiteSpace())
                    {
                        brandIds.Add(fbrandid);
                    }
                    else
                    {
                        var _seriIds = fserieid.SplitKey();
                        foreach (var seriId in _seriIds)
                        {
                            seriIds.Add(seriId);
                        }
                    }
                }
            }

            var brandSeriesX = prdAuths.SelectMany(f => f.fproductauthexclude).ToList();

            // 商品授权清单
            var productAuthKv = new Dictionary<string, HashSet<string>>();
            if (brandSeriesX.Count > 0)
            {
                // 如果存在多个【商品授权清单】 
                if (prdAuths.Count > 1)
                {
                    foreach (var item in prdAuths)
                    {
                        productAuthKv[item.fid] = new HashSet<string>(item.fproductauthexclude.Select(o => o.fproductid_o));
                    }
                }
            }

            foreach (var item in brandSeriesX)
            {
                if (item.fproductid_o.IsNullOrEmptyOrWhiteSpace()) continue;

                /*
                 * 如果存在多个【商品授权清单】，且该例外商品同时存在于其它所有商品授权清单的【例外商品】中时，
                 * 才需要排除，否则不需要排除。
                 * 
                 * 比如：商品A
                 * 在商品授权清单1中设置为【例外商品】
                 * 在商品授权清单2中没有设置为【例外商品】
                 * 那么商品A就不能被排除，也就是可以被选择使用。
                 */
                if (productAuthKv.Count > 0)
                {
                    if (CheckProductNeedExclude(productAuthKv, item))
                    {
                        var productIds = item.fproductid_o.SplitKey();
                        foreach (var productId in productIds)
                        {
                            excludeProdIds.Add(productId);
                        }
                    }
                }
                else
                {
                    var productIds = item.fproductid_o.SplitKey();
                    foreach (var productId in productIds)
                    {
                        excludeProdIds.Add(productId);
                    }
                }
            }

            brandSeries?.Clear();
            brandSeries = null;

            brandSeriesX?.Clear();
            brandSeriesX = null;

            foreach (var item in productAuthKv)
            {
                item.Value?.Clear();
            }
            productAuthKv.Clear();
            productAuthKv = null;
        }

        /// <summary>
        /// 检查例外商品是否需要排除
        /// </summary>
        /// <param name="productAuthKv">商品授权清单</param>
        /// <param name="excludeEntryData">例外商品</param>
        /// <returns></returns>
        private static bool CheckProductNeedExclude(Dictionary<string, HashSet<string>> productAuthKv, ProductAuthExclude excludeEntryData)
        {
            var needExclude = true;

            // 例外商品ID
            var productId = excludeEntryData.fproductid_o;

            // 例外商品所属的商品授权清单ID
            var prodcutAuthId = excludeEntryData.frootid;

            foreach (var item in productAuthKv)
            {
                // 例外商品本身所属的商品授权清单跳过不处理
                if (item.Key.EqualsIgnoreCase(prodcutAuthId)) continue;

                // 例外商品是否存在于其它所有商品授权清单的【例外商品】中
                if (!item.Value.Contains(productId))
                {
                    needExclude = false;
                    break;
                }
            }

            return needExclude;
        }

        /// <summary>
        /// 获取按商品授权的商品id
        /// </summary>
        /// <param name="prdAuths"></param>
        /// <returns></returns>
        private static HashSet<string> GetIncludeProdBrandIds(List<AgentProductAuthInfor> prdAuths)
        {
            var includeProdIds = new HashSet<string>();//可以看到的商品
            var products = prdAuths.SelectMany(f => f.fproductauthlist).ToList();
            foreach (var item in products)
            {
                if (!item.fbrandid.IsNullOrEmptyOrWhiteSpace())
                {
                    includeProdIds.Add(item.fbrandid);
                }
            }

            products?.Clear();
            products = null;

            return includeProdIds;
        }

        /// <summary>
        /// 获取按商品授权的对应系列id
        /// </summary>
        /// <param name="prdAuths"></param>
        /// <returns></returns>
        private static HashSet<string> GetIncludeProdSeriesIds(List<AgentProductAuthInfor> prdAuths)
        {
            var includeIds = new HashSet<string>();//可以看到的系列
            var products = prdAuths.SelectMany(f => f.fproductauthlist).ToList();
            foreach (var item in products)
            {
                if (!item.fseriesid.IsNullOrEmptyOrWhiteSpace())
                {
                    includeIds.Add(item.fseriesid);
                }
            }

            products?.Clear();
            products = null;

            return includeIds;
        }

        /// <summary>
        /// 获取按商品授权的商品对应的品牌信息
        /// </summary>
        /// <param name="prdAuths"></param>
        /// <returns></returns>
        private static List<ProductSalOrgInfo> GetIncludeProdIds(UserContext ctx, DataQueryRuleParaInfo rulePara, List<AgentProductAuthInfor> prdAuths)
        {
            var includeProdIds = new List<string>();//可以看到的商品
            var products = prdAuths.SelectMany(f => f.fproductauthlist).ToList();
            foreach (var item in products)
            {
                var fproductid = item.fproductid;
                if (fproductid.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                var canPur = item.fnopurchase;
                if (canPur && (rulePara.SrcFormId.EqualsIgnoreCase("ydj_purchaseorder") || rulePara.SrcFormId.EqualsIgnoreCase("stk_postockin")))
                {
                    //如果不允许采购，则采购订单、采购入库单不能选到该商品
                    continue;
                }
                includeProdIds.Add(fproductid);
            }

            var result = new List<ProductSalOrgInfo>();
            using (var tran = ctx.CreateTransaction())
            {
                var sql = GetAuthSql(ctx, includeProdIds, "fid");

                var dbService = ctx.Container.GetService<IDBService>();
                var datas = dbService.ExecuteDynamicObject(ctx, sql);

                tran.Complete();

                foreach (var mat in datas)
                {
                    var prdInfo = new ProductSalOrgInfo()
                    {
                        MatId = mat["fid"]?.ToString(),
                        MatNo = mat["fmatno"]?.ToString(),
                        MatName = mat["fmatname"]?.ToString(),
                        MainorgId = Convert.ToString(mat["fmainorgid"]),
                        SeriesId = Convert.ToString(mat["fseriesid"]),
                        SeriesNo = Convert.ToString(mat["fseriesno"]),
                    };

                    var fauxseriesid = Convert.ToString(mat["fauxseriesid"]);
                    if (fauxseriesid.IsNullOrEmptyOrWhiteSpace() == false)
                    {
                        var ids = fauxseriesid.SplitKey();
                        foreach (var item in ids)
                        {
                            if (item.IsNullOrEmptyOrWhiteSpace())
                            {
                                continue;
                            }

                            prdInfo.AuxSeriesid.Add(item);
                            prdInfo.AuxSeriesNo.Add(SerilBrandMapInfo.FirstOrDefault(f => f.SeriesId == item)?.SeriesNo);
                        }
                    }

                    result.Add(prdInfo);
                }

                datas?.Clear();
                datas = null;

            }

            includeProdIds.Clear();
            includeProdIds = null;
            products?.Clear();
            products = null;

            return result;
        }











    }

}