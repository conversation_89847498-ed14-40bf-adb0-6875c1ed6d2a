using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs.MerChant;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using static JieNor.AMS.YDJ.Store.AppService.MuSi.Api.MusiAuthService;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：同步
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("synctomusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 经销商经营模式信息
        /// key:经销商id,value:是否经营模式
        /// </summary>
        protected ConcurrentDictionary<string, bool> AgentManageModelDic { set; get; } = new ConcurrentDictionary<string, bool>();
        
        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            if (e.Entity == null || e.FieldEntry == null) return;

            var dataEntity = e.Entity;
            var bizEntity = e.DataEntity;

            string extFieldId = Convert.ToString(e.FieldEntry["fextfieldid"]).ToLower();

            switch (extFieldId)
            {
                // 合同附件集合
                case "ordfile":
                    {
                        e.Cancel = true;
                        e.Result = new List<OrderImgModel>();
                        if (!dataEntity["fimage"].IsNullOrEmptyOrWhiteSpace())
                        {
                            List<OrderImgModel> result = new List<OrderImgModel>();
                            var imgIds = Convert.ToString(dataEntity["fimage"]).Split(',');
                            var imgTxts = Convert.ToString(dataEntity["fimage_txt"]).Split(',');
                            for (int i = 0; i < imgIds.Length; i++)
                            {
                                if (!imgIds[i].IsNullOrEmptyOrWhiteSpace())
                                {
                                    var name = (i + 1 > imgTxts.Length ? imgIds[i] : imgTxts[i]);
                                    result.Add(new OrderImgModel
                                    {
                                        code = imgIds[i],
                                        name = name.IsNullOrEmptyOrWhiteSpace() ? imgIds[i] : name,
                                        fileAddr = imgIds[i]?.GetSignedFileUrl()
                                    });
                                }
                            }

                            e.Result = result;
                        }
                    }
                    break;
                // 配件父行号
                case "partsparnum":
                    {
                        // 根据《采购订单》单据体.商品明细行的【配件主商品】+【配件组合号】来判断, 如果商品行有【配件组合号】且【配件主商品】为否的, 都需要赋值套件父行号, 赋值逻辑为对应的【配件组合号】 且 有勾选上【配件主商品】的行号, 即为配件父行号
                        e.Cancel = true;
                        e.Result = "";

                        bool isCombMain = Convert.ToBoolean(dataEntity["fiscombmain"]);
                        string partsCombNumber = Convert.ToString(dataEntity["fpartscombnumber"]);

                        if (!partsCombNumber.IsNullOrEmptyOrWhiteSpace() && !isCombMain)
                        {
                            var entries = (DynamicObjectCollection)bizEntity["fentry"];

                            foreach (var entry in entries)
                            {
                                if (Convert.ToString(entry["fpartscombnumber"]).EqualsIgnoreCase(partsCombNumber) &&
                                    Convert.ToBoolean(entry["fiscombmain"]))
                                {
                                    e.Result = Convert.ToString(entry["fseq"]);
                                    break;
                                }
                            }
                        }
                    }
                    break;
                // 套件父行号
                case "suitparnum":
                    {
                        SetSuitPariNum(e,dataEntity, bizEntity);
                    }
                    break;
                // 沙发父行号
                case "sofaminnum":
                    {
                        SetSofaMiniNum(e,dataEntity, bizEntity);
                    }
                    break;
                // 消费者全地址
                case "cusfulladdr":
                    {
                        //todo:处理退货传参
                        var customer = dataEntity["fcustomerid_ref"] as DynamicObject;

                        var customerForm = this.UserContext.Container.GetService<IMetaModelService>()
                            .LoadFormModel(this.UserContext, "ydj_customer");
                        var refMgr = this.UserContext.Container.GetService<LoadReferenceObjectManager>();
                        refMgr.Load(this.UserContext, customerForm.GetDynamicObjectType(this.UserContext), customer, false);

                        string provinceName = Convert.ToString((customer?["fprovince_ref"] as DynamicObject)?["fenumitem"]);
                        string cityName = Convert.ToString((customer?["fcity_ref"] as DynamicObject)?["fenumitem"]);
                        string regionName = Convert.ToString((customer?["fregion_ref"] as DynamicObject)?["fenumitem"]);
                        string address = Convert.ToString(customer?["faddress"]);

                        e.Cancel = true;
                        //todo: 处理退货传参
                        var orderbyreturn = Convert.ToBoolean(Convert.ToInt32(dataEntity["forderbyreturn"]));
                        if (orderbyreturn)
                        {
                            e.Result = $"{provinceName}{cityName}{regionName}{Convert.ToString(dataEntity["faddress"])}";
                        }
                        else
                        {
                            e.Result = $"{provinceName}{cityName}{regionName}{address}";
                        }
                    }
                    break;
                case "saletocode":
                    {
                        e.Cancel = true;
                        var agentDy = this.UserContext.LoadBizBillHeadDataById("bas_agent", this.UserContext.Company, "fnumber,fmanagemodel");
                        if (agentDy != null)
                        {
                            var fmanagemodel = Convert.ToString(agentDy["fmanagemodel"]);
                            if (!fmanagemodel.IsNullOrEmptyOrWhiteSpace() && fmanagemodel.Equals("1"))
                            {
                                e.Cancel = true;
                                e.Result = Convert.ToString(agentDy["fnumber"]);
                            }
                            else
                            {
                                e.Result = GetCurrAgentNumber(e.ReferenceDataEntityMaps, Convert.ToString(dataEntity["fmainorgid"]),
                                    e.DataEntity);
                            }
                        }
                        else
                        {
                            e.Result = GetCurrAgentNumber(e.ReferenceDataEntityMaps, Convert.ToString(dataEntity["fmainorgid"]),
                                e.DataEntity);
                        }
                    }
                    break;
                case "uppersaletocode":
                    {
                        e.Cancel = true;
                        e.Result = GetParentAgentFieldValue(e.ReferenceDataEntityMaps,
                            Convert.ToString(dataEntity["fmainorgid"]), "fnumber");
                    }
                    break;
                case "uppersaletoname":
                    {
                        e.Cancel = true;
                        e.Result = GetParentAgentFieldValue(e.ReferenceDataEntityMaps,
                            Convert.ToString(dataEntity["fmainorgid"]), "fname");
                    }
                    break;
                case "accountnum":
                    {
                        e.Cancel = true;
                        e.Result = GetAccountFieldValue(e.ReferenceDataEntityMaps, bizEntity,
                            Convert.ToString(dataEntity["fmainorgid"]), "fnumber");
                    }
                    break;
                case "accountname":
                    {
                        e.Cancel = true;
                        e.Result = GetAccountFieldValue(e.ReferenceDataEntityMaps, bizEntity,
                            Convert.ToString(dataEntity["fmainorgid"]), "fname");
                    }
                    break;
                case "thflag": //直发标记

                    e.Cancel = true;
                    e.Result = "";

                    e.Result = Convert.ToString(dataEntity["fsendtarget"]);
                    break;
                case "urgentflag": //加急标识

                    e.Cancel = true;
                    e.Result = "";

                    e.Result = Convert.ToInt32(dataEntity["furgent"]);
                    break;
                case "cusname":
                    e.Cancel = true;
                    e.Result = GetCustomer(bizEntity, "fname");
                    break;
                case "cusph":
                    e.Cancel = true;
                    e.Result = GetCustomer(bizEntity, "fphone");
                    break;
                case "refmemberid": //推荐人会员ID
                    e.Cancel = true;
                    var data = GetMembernoByPhone(Convert.ToString(dataEntity["fcustomerid"]));
                    if (data != null && data.Count > 0)
                    {
                        var fmemberno = data.Select(x => Convert.ToString(x["fmemberno"])).FirstOrDefault();
                        e.Result = fmemberno;
                    }

                    break;
                case "referrer": //推荐人
                    e.Cancel = true;
                    var data1 = GetMembernoByPhone(Convert.ToString(dataEntity["fcustomerid"]));
                    if (data1 != null && data1.Count > 0)
                    {
                        var fname = data1.Select(x => Convert.ToString(x["fname"])).FirstOrDefault();
                        e.Result = fname;
                    }

                    break;
                case "rcmdph": //推荐人电话
                    e.Cancel = true;
                    var data2 = GetMembernoByPhone(Convert.ToString(dataEntity["fcustomerid"]));
                    if (data2 != null && data2.Count > 0)
                    {
                        var fphone = data2.Select(x => Convert.ToString(x["fphone"])).FirstOrDefault();
                        e.Result = fphone;
                    }

                    break;
                case "salesmanid": //销售员Id
                    {
                        e.Cancel = true;
                        e.Result = "";

                        var staffidObj = dataEntity["fstaffid_ref"] as DynamicObject;
                        if (staffidObj == null && !Convert.ToString(dataEntity["fstaffid"]).IsNullOrEmptyOrWhiteSpace())
                        {
                            dataEntity["fstaffid_ref"] = staffidObj =
                                this.UserContext.LoadBizDataById("ydj_staff", Convert.ToString(dataEntity["fstaffid"]));
                        }

                        e.Result = Convert.ToString(staffidObj?["Id"]);
                    }
                    break;
                case "salesman": //销售员
                    {
                        e.Cancel = true;
                        e.Result = "";

                        var staffidObj = dataEntity["fstaffid_ref"] as DynamicObject;
                        if (staffidObj == null && !Convert.ToString(dataEntity["fstaffid"]).IsNullOrEmptyOrWhiteSpace())
                        {
                            dataEntity["fstaffid_ref"] = staffidObj =
                                this.UserContext.LoadBizDataById("ydj_staff", Convert.ToString(dataEntity["fstaffid"]));
                        }

                        e.Result = Convert.ToString(staffidObj?["fname"]);
                    }
                    break;
                case "salesmanphone": //销售员电话
                    {
                        e.Cancel = true;
                        e.Result = "";
                        var staffidObj = dataEntity["fstaffid_ref"] as DynamicObject;
                        if (staffidObj == null && !Convert.ToString(dataEntity["fstaffid"]).IsNullOrEmptyOrWhiteSpace())
                        {
                            dataEntity["fstaffid_ref"] = staffidObj =
                                this.UserContext.LoadBizDataById("ydj_staff", Convert.ToString(dataEntity["fstaffid"]));
                        }

                        e.Result = Convert.ToString(staffidObj?["fphone"]);
                    }
                    break;
                case "dealercode":
                    //焕新订单标记
                    var frenewalflag = Convert.ToBoolean(dataEntity?["frenewalflag"]);
                    //【关联门店】对应《门店》表头的【招商经销商】传给中台, 如果该部门没有【关联门店】则默认当前企业对应的经销商的《经销商》表头的【招商经销商】 (如果存在多个时随机给一个就行, 但保证每次给都是同一个就行)
                    var dealerObj = frenewalflag ? GetStore(dataEntity) : GetDealer(dataEntity);

                    e.Result = Convert.ToString(dealerObj?["fnumber"]);
                    e.Cancel = true;
                    break;
                case "dealername":
                    dealerObj = GetDealer(dataEntity);
                    e.Result = Convert.ToString(dealerObj?["fname"]);
                    e.Cancel = true;
                    break;
                case "rcvcusname": //收货人

                    e.Cancel = true;
                    e.Result = "";

                    e.Result = (dataEntity["fcustomercontactid_ref"] as DynamicObject)?["fcontacter"];
                    break;
                case "attributecode":
                    e.Cancel = true;
                    e.Result = "";
                    if (!string.IsNullOrWhiteSpace(Convert.ToString(dataEntity["forderattr"])))
                    {
                        e.Result = Convert.ToInt32(dataEntity["forderattr"]);
                    }

                    break;
                case "relordnum":
                    e.Cancel = true;
                    e.Result = Convert.ToString(dataEntity["fprojectnumber"]);
                    break;
                case "factoryresponsibilityflag":
                    e.Cancel = true;
                    e.Result = Convert.ToInt32(dataEntity["faftserviceisfree"]);
                    break;
                case "servicereasons":
                    e.Cancel = true;
                    string aftreason = "";
                    switch (Convert.ToString(dataEntity["faftreason"]))
                    {
                        case "aftreason_1":
                            aftreason = "1";
                            break;
                        case "aftreason_2":
                            aftreason = "2";
                            break;
                        case "aftreason_3":
                            aftreason = "3";
                            break;
                        case "aftreason_4":
                            aftreason = "4";
                            break;
                        case "aftreason_5":
                            aftreason = "5";
                            break;
                        default:
                            break;
                    }

                    e.Result = aftreason;
                    break;
                case "zyzzp":
                    e.Cancel = true;
                    e.Result = Convert.ToString(e.DataEntity["finvoicetype"]);
                    break;
                //分销渠道(传经营模式)
                case "zyvtweg":
                    e.Cancel = true;
                    e.Result = GetOrgManageModel(e.DataEntity);
                    break;
                case "dealertype":
                    e.Cancel = true;
                    e.Result = GetOrgManageModel(e.DataEntity);
                    break;
                case "zyitemratio":
                    e.Cancel = true;
                    e.Result = GenerateDutyInfo(e.DataEntity);
                    break;
                //订单凭证类型
                case "zytype":
                    e.Cancel = true;
                    e.Result = GetDirectSaleType(e.DataEntity);
                    break;
                //合作渠道
                case "zyzhzqd":
                    e.Cancel = true;
                    e.Result = GetChannelSupplierCode(e.DataEntity);
                    break;
                //抽佣比例
                case "zyzhzqdcy":
                    e.Cancel = true;
                    var priceRatio = GetCannelPriceRatio(e.DataEntity, dataEntity);
                    e.Result = priceRatio;
                    break;
                case "zykbetrzk91":
                    e.Cancel = true;
                    e.Result = GetZyKbetrzk91(dataEntity);
                    break;
                case "zyinvloc":
                    e.Cancel = true;
                    e.Result = GetZyInvLocNumber(dataEntity, e.DataEntity);
                    break;
                case "salorgcode":
                    e.Cancel = true;
                    e.Result = GetSaleOrgCode(e.DataEntity);
                    break;
                //传：Y / N 部分与未收款：传N(直营)
                case "paymentstatus":
                    if (IsDirectSaleManageModel(e.DataEntity))
                    {
                        e.Cancel = true;
                        e.Result = GetPayStatus(e.DataEntity);
                    }
                    break;
                case "zyzsfkp":
                    e.Cancel = true;
                    e.Result = GetIsNeedInvoice(e.DataEntity);
                    break;
                //门店自提
                case "zyzmdzt":
                    e.Cancel = true;
                    e.Result = GetZyZmdzt(dataEntity);
                    break;
                case "sourcenum":
                    e.Cancel = true;
                    e.Result = GetRelevanceOrderNo(e.DataEntity);
                    break;
                /*case "discpct":
                    e.Cancel = true;
                    e.Result = GetDiscPct(e.DataEntity, dataEntity);
                    break;*/
                case "discprice":
                    e.Cancel = true;
                    e.Result = GetDisOnePrice(e.DataEntity, dataEntity);
                    break;
                //产品定制类型(01:标准品、02:标准定制、03:非标定制)
                case "zyprodtype":
                    SetRequestDataZyProdTypeValue(e, dataEntity);
                    break;
                case "crtname":
                    SetRequestDataCrtName(e, e.DataEntity);
                    break;
                case "shiptocode":
                    {
                        var agentDy = this.UserContext.LoadBizBillHeadDataById("bas_agent", this.UserContext.Company, "fmanagemodel");
                        if (agentDy != null)
                        {
                            var fmanagemodel = Convert.ToString(agentDy["fmanagemodel"]);
                            if (!fmanagemodel.IsNullOrEmptyOrWhiteSpace() && fmanagemodel.Equals("1"))
                            {
                                e.Cancel = true;
                                e.Result = GetDeliver(e.DataEntity);
                            }
                        }
                    }

                    break;
            }
        }

        /// <summary>
        /// 获取门店编码
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private DynamicObject GetStore(DynamicObject dataEntities)
        {
            var deptId = Convert.ToString(dataEntities["fdeptid"]);
            if (deptId == null)
            {
                return null;
            }

            //获取合同对应门店
            var store = Convert.ToString(dataEntities?["fstore"]);
            //焕新订单标记
            var frenewalflag = Convert.ToBoolean(dataEntities?["frenewalflag"]);

            //焕新订单返回门店编码
            var strSql = $@"SELECT top 1 store.fnumber  FROM t_bd_department dept 
                        INNER JOIN dbo.T_BAS_STORE store ON store.fid = dept.fstore
                        WHERE dept.fid = '{deptId}' AND dept.fmainorgid ='{this.UserContext.Company}' ";

            var dbService = this.UserContext.Container.GetService<IDBService>();
            var DealerObj = dbService.ExecuteDynamicObject(this.UserContext, strSql).FirstOrDefault();

            return DealerObj;
        }

        /// <summary>
        /// 获取部门对应门店记录的 招商经销商
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private DynamicObject GetDealer(DynamicObject dataEntities)
        {
            var deptId = Convert.ToString(dataEntities["fdeptid"]);
            if (deptId == null)
            {
                return null;
            }

            //获取合同对应门店
            var store = Convert.ToString(dataEntities?["fstore"]);
            DynamicObject DealerObj = null;
            // 如果该部门没有【关联门店】则默认当前企业对应的经销商的《经销商》表头的【招商经销商】 (如果存在多个时随机给一个就行, 但保证每次给都是同一个就行)
            if (store.IsNullOrEmptyOrWhiteSpace())
            {
                return GetDealerByAgent();
            }
            else
            {
                var strSql =
                    $@"SELECT top 1 t_ms_crmdistributor.fid,t_ms_crmdistributor.fnumber,t_ms_crmdistributor.fname,store.fnumber as stroreNumber FROM t_bd_department dept 
                        INNER JOIN dbo.T_BAS_STORE store ON store.fid = dept.fstore
                        INNER JOIN t_ms_crmdistributor ON t_ms_crmdistributor.fid = store.foutcrmdistributorid
                        WHERE dept.fid = '{deptId}' AND dept.fmainorgid ='{this.UserContext.Company}' ";

                var dbService = this.UserContext.Container.GetService<IDBService>();
                DealerObj = dbService.ExecuteDynamicObject(this.UserContext, strSql).FirstOrDefault();

                //如果门店的招商经销商没有值  再走经销商 的逻辑
                if (Convert.ToString(DealerObj?["fname"]).IsNullOrEmptyOrWhiteSpace())
                {
                    return GetDealerByAgent();
                }
            }

            return DealerObj;
        }

        private DynamicObject GetDealerByAgent()
        {
            DynamicObject DealerObj = null;
            GetResultBrandData re = new GetResultBrandData();
            var crmdistributorids = re.GetBaseDataNameById(this.UserContext, "bas_agent", this.UserContext.Company,
                "fcrmdistributorid");
            var crmdistributoridLst = crmdistributorids.Split(',');
            if (crmdistributoridLst.Length > 0)
            {
                var crmdistributorid = Convert.ToString(crmdistributoridLst[0]);
                DealerObj = this.UserContext.LoadBizBillHeadDataById("ms_crmdistributor", crmdistributorid,
                    "fid,fnumber,fname");
            }

            return DealerObj;
        }

        /// <summary>
        /// 得到推荐人
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection GetMembernoByPhone(string cid)
        {
            string strSql = $@"select top 1 t2.fmemberno,t2.fname,t2.fphone from t_ydj_customer t1 with(nolock)
            join t_ydj_customer t2 with(nolock) on t1.freferrer = t2.fid
            where t1.fid = '{cid}'";
            return UserContext.ExecuteDynamicObject(strSql, new List<SqlParam>() { });
        }

        private string GetCustomer(DynamicObject order, string column)
        {
            //todo: 处理退货传参
            var orderbyreturn = Convert.ToBoolean(Convert.ToInt32(order["forderbyreturn"]));
            if (orderbyreturn)
            {
                var customer = order["fcustomerid_ref"] as DynamicObject;
                return Convert.ToString(customer?[column]);
            }
            else
            {
                //如果【终端客户】为空时, 则获取《销售合同》单据头-基本信息【客户】的名称赋值
                //如果【终端客户】不为空时, 则获取《销售合同》单据头-终端客户【终端客户】的名称赋值
                if (Convert.ToString(order?["fterminalcustomer"]).IsNullOrEmptyOrWhiteSpace())
                {
                    var customer = order["fcustomerid_ref"] as DynamicObject;
                    return Convert.ToString(customer?[column]);
                }
                else
                {
                    var customerzd = order["fterminalcustomer_ref"] as DynamicObject;
                    return Convert.ToString(customerzd?[column]);
                }
            }
        }

        /// <summary>
        /// 打包前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforePackSourceBill(BeforePackSourceBillEventArgs e)
        {
            base.BeforePackSourceBill(e);

            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            SetAgentManageModelDic(this.UserContext,e.DataEntitys);
            
            // 获取合同附件
            var orders = e.DataEntitys;

            List<string> ordIds = orders.Select(s => Convert.ToString(s["id"])).ToList();
            DynamicObjectCollection secOrderIdMap = null;
            Dictionary<string, string> ordMapSecOrd = null;

            //二级分销合同且【需转单】时, 一级经销商的《销售合同》要获取二级分销商的《采购订单》上游《销售合同》的附件
            //同样复制到 一级经销商的《销售合同》上可以查看, 最终目的是该 一级经销商的《销售合同》进行转单后, 要在中台可以查看到该附件
            if (!this.UserContext.IsSecondOrg)
            {
                var secNeedOrders = e.DataEntitys.Where(x => Convert.ToBoolean(x["fisresellorder"])
                                                             && Convert.ToBoolean(x["fneedtransferorder"]));
                if (secNeedOrders != null && secNeedOrders.Any())
                {
                    secOrderIdMap = this.UserContext.ExecuteDynamicObject(
                        $@"select t0.fid,t2.fid fsecordid from t_ydj_order t0 with(nolock)
						inner join t_ydj_purchaseorder t1 with(nolock) on t0.fsourceid=t1.fid
						inner join t_ydj_order t2 with(nolock) on t1.fsourcenumber=t2.fbillno and t1.fmainorgid=t2.fmainorgid
						where t0.fisresellorder='1' and t0.fid in ({string.Join(",", secNeedOrders.Select(s => $"'{Convert.ToString(s["id"])}'"))})",
                        null);
                    if (secOrderIdMap != null && secOrderIdMap.Any())
                    {
                        ordMapSecOrd = secOrderIdMap.ToDictionary(k => Convert.ToString(k["fid"]),
                            v => Convert.ToString(v["fsecordid"]));
                        ordIds.AddRange(secOrderIdMap.Select(x => Convert.ToString(x["fsecordid"])));
                    }
                }
            }

            var attachlist = this.UserContext.LoadBizDataByFilter("bd_attachlist",
                $" flinkbillinterid in ({string.Join(",", ordIds.Select(s => $"'{s}'"))})");

            foreach (var order in orders)
            {
                //取消接口排序逻辑
                //SortBysuit(order["fentry"] as DynamicObjectCollection);
                var drawEntrys = order["fdrawentity"] as DynamicObjectCollection;
                string orderId = Convert.ToString(order["id"]);
                string secOrderId = ordMapSecOrd?[orderId];
                var attach =
                    attachlist.FirstOrDefault(s => Convert.ToString(s["flinkbillinterid"]).EqualsIgnoreCase(orderId));

                drawEntrys.Clear();

                if (attach != null)
                {
                    var entrys = attach["fdrawentity"] as DynamicObjectCollection;
                    foreach (var entry in entrys)
                    {
                        var drawEntry = drawEntrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

                        drawEntry["ffilename"] = entry["ffilename"];
                        drawEntry["ffileid"] = entry["ffileid"];
                        drawEntry["ffileformat"] = entry["ffileformat"];
                        drawEntry["ffilesize"] = entry["ffilesize"];
                        drawEntry["fnote"] = entry["fnote"];
                        drawEntry["fuploader"] = entry["fuploader"];
                        drawEntry["fuploaderid"] = entry["fuploaderid"];
                        drawEntry["fuptime"] = entry["fuploadtime"];
                        drawEntry["fsourceentryid"] = entry["fsourceentryid"];

                        drawEntrys.Add(drawEntry);
                    }
                }

                if (secOrderId != null)
                {
                    attach =
                        attachlist.FirstOrDefault(s =>
                            Convert.ToString(s["flinkbillinterid"]).EqualsIgnoreCase(secOrderId));
                    if (attach != null)
                    {
                        var entrys = attach["fdrawentity"] as DynamicObjectCollection;
                        foreach (var entry in entrys)
                        {
                            var drawEntry =
                                drawEntrys.DynamicCollectionItemPropertyType.CreateInstance() as DynamicObject;

                            drawEntry["ffilename"] = entry["ffilename"];
                            drawEntry["ffileid"] = entry["ffileid"];
                            drawEntry["ffileformat"] = entry["ffileformat"];
                            drawEntry["ffilesize"] = entry["ffilesize"];
                            drawEntry["fnote"] = entry["fnote"];
                            drawEntry["fuploader"] = entry["fuploader"];
                            drawEntry["fuploaderid"] = entry["fuploaderid"];
                            drawEntry["fuptime"] = entry["fuploadtime"];
                            drawEntry["fsourceentryid"] = entry["fsourceentryid"];

                            drawEntrys.Add(drawEntry);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 根据套件商品做排序
        /// </summary>
        /// <param name="fentry"></param>
        private void SortBysuit(DynamicObjectCollection fentry)
        {
            if (!fentry.IsNullOrEmpty())
            {
                var entrys = fentry
                    .OrderByDescending(f => Convert.ToInt32((f["fproductid_ref"] as DynamicObject)?["fsuiteflag"]))
                    .OrderBy(g => Convert.ToString(g["fsuitcombnumber"])).ToList();
                fentry.Clear();
                for (int i = 0; i < entrys.Count; i++)
                {
                    var entry = entrys[i] as DynamicObject;
                    //传过去的行也做排序
                    entry["fseq"] = i + 1;
                    fentry.Add(entry);
                }
            }
        }

        /// <summary>
        /// 来源单据打包后事件
        /// </summary>
        /// <param name="e"></param>
        public override void AfterPackSourceBill(AfterPackSourceBillEventArgs e)
        {
            base.AfterPackSourceBill(e);

            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            // 获取合同附件
            var orders = e.DataEntitys;

            foreach (var order in orders)
            {
                var drawEntrys = order["fdrawentity"] as DynamicObjectCollection;
                drawEntrys.Clear();
            }
        }

        /// <summary>
        /// 获取所属组织编码
        /// </summary>
        /// <param name="mainOrgId"></param>
        /// <returns></returns>
        private string GetCurrAgentNumber(Dictionary<string, Dictionary<string, DynamicObject>> maps, string mainOrgId,
            DynamicObject orderDy)
        {
            var currMainOrg = GetReferenceDataEntity(maps, "bas_agent", mainOrgId);
            if (currMainOrg == null) return string.Empty;
            var fmanagemodel = Convert.ToString(currMainOrg["fmanagemodel"]);
            //1直营，0经销商
            if (fmanagemodel.EqualsIgnoreCase("1"))
            {
                return GetDirectSaleToCOde(orderDy);
            }
            else
            {
                /*
                 * 根据当前企业对应的《经销商》是否勾选上【是否分销商】来判断
                 * 1. 如果当前企业对应的《经销商》勾选上【是否分销商】时, 则不需要赋值
                 * 2. 如果没勾选上时, 则与现在原本的逻辑相同
                 */

                var fisreseller = Convert.ToBoolean(currMainOrg["fisreseller"]);

                if (fisreseller)
                {
                    return string.Empty;
                }

                return Convert.ToString(currMainOrg["fnumber"]);
            }
        }

        /// <summary>
        /// 获取上级经销商的字段值
        /// </summary>
        /// <param name="mainOrgId"></param>
        /// <param name="fieldId"></param>
        /// <returns></returns>
        private string GetParentAgentFieldValue(Dictionary<string, Dictionary<string, DynamicObject>> maps,
            string mainOrgId, string fieldId)
        {
            var currMainOrg = GetReferenceDataEntity(maps, "bas_agent", mainOrgId);
            if (currMainOrg == null) return string.Empty;

            /*
             * 根据当前企业对应的《经销商》是否勾选上【是否分销商】来判断
             * 1. 如果对应的《经销商》勾选上【是否分销商】时, 则获取【所属上级组织】赋值
             * 2. 如果没勾选上时, 则不赋值
             */

            var fisreseller = Convert.ToBoolean(currMainOrg["fisreseller"]);

            if (fisreseller)
            {
                var parentMainOrgId = Convert.ToString(currMainOrg["forgid"]);
                var parentMainOrg = GetReferenceDataEntity(maps, "bas_agent", parentMainOrgId);

                return Convert.ToString(parentMainOrg?[fieldId]);
            }

            return string.Empty;
        }

        /// <summary>
        /// 获取大客户的字段值
        /// </summary>
        /// <param name="mainOrgId"></param>
        /// <param name="fieldId"></param>
        /// <returns></returns>
        private string GetAccountFieldValue(Dictionary<string, Dictionary<string, DynamicObject>> maps,
            DynamicObject order, string mainOrgId, string fieldId)
        {
            /*
             * 根据当前企业对应的《经销商》是否勾选上【是否分销商】来判断
             * 1. 如果《销售合同》表头的【合作渠道】不为空  且 当前企业对应的《经销商》没有勾选上【是否分销商】时, 则获取【合作渠道】的编码进行赋值
             * 2. 如果当前企业对应的《经销商》勾选上【是否分销商】时, 则获取当前企业对应的《经销商》的编码进行赋值
             * 3.如果都不为上述情况时, 则默认不赋值
             */

            var currMainOrg = GetReferenceDataEntity(maps, "bas_agent", mainOrgId);
            if (currMainOrg == null) return string.Empty;

            // 是否分销商
            var fisreseller = Convert.ToBoolean(currMainOrg["fisreseller"]);
            // 合作渠道
            var channelId = Convert.ToString(order["fchannel"]);

            // 情况1：如果《销售合同》表头的【合作渠道】不为空  且 当前企业对应的《经销商》没有勾选上【是否分销商】时, 则获取【合作渠道】进行赋值
            if (!channelId.IsNullOrEmptyOrWhiteSpace() && !fisreseller)
            {
                var channel = GetReferenceDataEntity(maps, "ste_channel", channelId);
                return Convert.ToString(channel?[fieldId]);
            }

            // 情况2：如果当前企业对应的《经销商》勾选上【是否分销商】时, 则获取当前企业对应的《经销商》进行赋值
            if (fisreseller)
            {
                return Convert.ToString(currMainOrg[fieldId]);
            }

            return string.Empty;
        }

        /// <summary>
        /// 获取联合开单中的信息，并放在明细行传过去
        /// </summary>
        /// <param name="orderDy">销售合同数据包</param>
        /// <returns></returns>
        private JArray GenerateDutyInfo(DynamicObject orderDy)
        {
            var dutyInfo = new JArray();

            var dutyEntrys = orderDy["fdutyentry"] as DynamicObjectCollection;

            foreach (var dutyEntry in dutyEntrys)
            {
                var staffDy = dutyEntry["fdutyid_ref"] as DynamicObject;

                var deptDy = dutyEntry["fdeptid_ref"] as DynamicObject;

                var info = new JObject();

                //直营带单员
                info["zyPernrSales"] = Convert.ToString(staffDy?["fnumber"]);

                //业务员电话
                info["zyTelSales"] = Convert.ToString(staffDy?["fnumber"]);

                //业务员姓名
                info["zyNameSales"] = Convert.ToString(staffDy?["fname"]);

                //分成占比
                info["zyRatio"] = Convert.ToString(dutyEntry["fratio"]);

                //业绩占比
                info["zyPreRatio"] = Convert.ToString(dutyEntry["fdeptperfratio"]);

                //部门编码
                info["zyDeptNo"] = Convert.ToString(deptDy?["fnumber"]);

                //部门名称
                info["zyDeptName"] = Convert.ToString(deptDy?["fname"]);

                dutyInfo.Add(info);
            }

            return dutyInfo;
        }

        /// <summary>
        /// 映射获取销售凭证类型
        ///    ZY01	直营标准销售订单
        ///    ZY02	直营退货销售订单
        ///    ZY03	维修退回销售订单
        ///    ZY04	维修发直营货销售订单
        ///    ZY05	免费维修发货销售订单
        ///    ZY06	直营免费销售订单
        ///    ZY07	直营样品备货订单（备货采购，样品备货）
        ///    ZY08	直营样品销售订单
        ///    ZY10	总部直发销售单
        /// </summary>
        /// <param name="orderDy">销售合同数据包</param>
        /// <returns></returns>
        private string GetDirectSaleType(DynamicObject orderDy)
        {
            string typeStr = string.Empty;

            var orderbyreturn = Convert.ToBoolean(Convert.ToInt32(orderDy["forderbyreturn"]));
            if (orderbyreturn)
            {
                typeStr = "ZY02";
            }
            else
            {
                //一件代发标记
                var isPiecesSendTag = Convert.ToBoolean(Convert.ToInt32(orderDy["fpiecesendtag"]));
                if (isPiecesSendTag)
                {
                    //ZY10 总部直发销售单
                    typeStr = "ZY10";
                }
                else
                {
                    //ZY01	直营标准销售订单
                    typeStr = "ZY01";
                }
            }

            return typeStr;
        }

        /// <summary>
        /// 获取明细行中的仓库
        /// </summary>
        /// <param name="productEntry">销售合同的商品明细行</param>
        /// <param name="orderDy">销售合同数据包</param>
        /// <returns></returns>
        private string GetZyInvLocNumber(DynamicObject productEntry, DynamicObject orderDy)
        {
            string zyInvLocNumber = string.Empty;
            var storeHouseId = Convert.ToString(productEntry["fstorehouseid"]);
            var isoutspot = Convert.ToBoolean(Convert.ToInt32(productEntry["fisoutspot"]));
            if (isoutspot)
            {
                //出现货传销售合同上的门店编码
                /*var storeId = Convert.ToString(orderDy["fstore"]);
                if (storeId.IsNullOrEmptyOrWhiteSpace())
                {
                    return zyInvLocNumber;
                }
                else
                {
                    var storeDy = orderDy["fstore_ref"] as DynamicObject;
                    if (storeDy == null)
                    {
                        storeDy = this.UserContext.LoadBizBillHeadDataById("bas_store", storeHouseId, "fnumber,fname");
                    }

                    if (storeDy == null)
                    {
                        return zyInvLocNumber;
                    }

                    zyInvLocNumber = Convert.ToString(storeDy["fnumber"]);
                    return zyInvLocNumber;
                }*/


                if (storeHouseId.IsNullOrEmptyOrWhiteSpace())
                {
                    return zyInvLocNumber;
                }
                var storeHouseDy = productEntry["fstorehouseid_ref"] as DynamicObject;
                if (storeHouseDy == null)
                {
                    storeHouseDy = this.UserContext.LoadBizBillHeadDataById("ydj_storehouse", storeHouseId, "fnumber,fname,fwarehousetype");
                }
                if (storeHouseDy == null)
                {
                    return zyInvLocNumber;
                }
                // 1。如果仓库的类型是门店仓，取销售合同上面的单据头门店编码
                // 2。如果仓库的类型是总仓，传总仓编码
                var wareHouseType = Convert.ToString(storeHouseDy["fwarehousetype"]);
                //warehouse_01 总仓 warehouse_02 门店仓
                if (wareHouseType.Equals("warehouse_01"))
                {
                    zyInvLocNumber = Convert.ToString(storeHouseDy["fnumber"]);
                }
                else
                {
                    var storeId = Convert.ToString(orderDy["fstore"]);
                    if (storeId.IsNullOrEmptyOrWhiteSpace())
                    {
                        return zyInvLocNumber;
                    }
                    else
                    {
                        var storeDy = orderDy["fstore_ref"] as DynamicObject;
                        if (storeDy == null)
                        {
                            storeDy = this.UserContext.LoadBizBillHeadDataById("bas_store", storeId, "fnumber,fname");
                        }
                        if (storeDy == null)
                        {
                            return zyInvLocNumber;
                        }
                        zyInvLocNumber = Convert.ToString(storeDy["fnumber"]);
                        return zyInvLocNumber;
                    }
                }
            }
            else
            {
                var pieceSendTag = Convert.ToBoolean(Convert.ToInt32(orderDy["fpiecesendtag"]));
                if (!pieceSendTag)
                {
                    // ③存在明细行【出现货≠勾选】，销售凭证类型=ZY01，门店自提=N，库存地点=空，一件代发标志=N
                    string _sqlStr = $"select top 1 fid,fnumber from T_YDJ_STOREHOUSE with(nolock) where  fmainorgid='{this.UserContext.Company}' and fwarehousetype='warehouse_01' and fforbidstatus=0  and fcreatorid='sysadmin' order by fcreatedate asc";
                    using (var dr = this.UserContext.ExecuteReader(_sqlStr, null))
                    {
                        if (dr.Read())
                        {
                            zyInvLocNumber = Convert.ToString(dr["fnumber"]).Trim();
                        }
                    }
                }

            }


            return zyInvLocNumber;
        }

        /// <summary>
        /// 获取佣金比例中的
        /// </summary>
        /// <param name="orderDy"></param>
        /// <returns></returns>
        private string GetCannelPriceRatio(DynamicObject orderDy, DynamicObject productEntry)
        {
            string priceRatio = string.Empty;

            /*if (!Convert.ToString(orderDy["fchannel"]).IsNullOrEmptyOrWhiteSpace())
            {
                var channelDy = orderDy["fchannel_ref"] as DynamicObject;
                if (channelDy == null)
                {
                    var channelId = Convert.ToString(orderDy["fchannel"]);
                    var mainOrgId = Convert.ToString(orderDy["fmainorgid"]);
                    var agentDbContext = this.UserContext.CreateCompanyDbContext(mainOrgId);
                    // agentDbContext.LoadDataById("")
                    channelDy = agentDbContext.LoadBizDataById("ste_channel",channelId);
                }

                //累计带单金额
                var sumBillAmount = Convert.ToDecimal(channelDy["fsumbillamount"]);
                if (sumBillAmount > 0)
                {
                    var billAmountEntry = channelDy["fentry"] as DynamicObjectCollection;
                    if(billAmountEntry != null && billAmountEntry.Any())
                    {
                        var findEntry = billAmountEntry.FirstOrDefault(x=>Convert.ToDecimal(x["fupperlimit"])>= sumBillAmount);
                        if (findEntry != null)
                        {
                            priceRatio = Convert.ToString(findEntry["fratio"]);
                        }
                    }
                }
            }*/

            //使用当前的应付佣金/销售总额，然后传给中台
            /*if (Convert.ToDecimal(orderDy["fsumamount"]) <= 0)
            {
                priceRatio = "0";
            }
            else
            {
                var planBrokerAge = Convert.ToDecimal(orderDy["fplanbrokerage"]);
                var sumAmount = Convert.ToDecimal(orderDy["fsumamount"]);
                // planBrokerAge * 100 / sumAmount
                var ratio = decimal.Round(planBrokerAge * 100 / sumAmount, 2);
                priceRatio = Convert.ToString(ratio);
            }*/

            priceRatio = Convert.ToString(productEntry["fcommissionrate_e"]);

            return priceRatio;
        }

        /// <summary>
        /// 获取当前数据包的经营模式
        /// </summary>
        /// <param name="customerDy"></param>
        /// <returns></returns>
        private string GetOrgManageModel(DynamicObject orderDy)
        {
            var manageModelStr = string.Empty;
            //'0':'经销商','1':'直营'
            //var fmanagemodel = Convert.ToString(customerDy["fmanagemodel"]);
            var agentId = Convert.ToString(orderDy["fmainorgid"]);
            if (!agentId.IsNullOrEmptyOrWhiteSpace())
            {
                if (IsDirectSaleManageModel(orderDy))
                {
                    manageModelStr = "10";
                }
                else
                {
                    manageModelStr = "20";
                }
            }
            else
            {
                manageModelStr = "20";
            }

            return manageModelStr;
        }

        /// <summary>
        /// 获取当前数据包所在的经销商档案的销售组织编码
        /// </summary>
        /// <param name="orderDy"></param>
        /// <returns></returns>
        private string GetSaleOrgCode(DynamicObject orderDy)
        {
            var saleOrgId = string.Empty;
            var agentId = Convert.ToString(orderDy["fmainorgid"]);
            if (!agentId.IsNullOrEmptyOrWhiteSpace())
            {
                var agentDy = this.UserContext.LoadBizBillHeadDataById("bas_agent", agentId, "fsaleorgid");
                if (agentDy != null)
                {
                    saleOrgId = Convert.ToString(agentDy["fsaleorgid"]);
                    var saleOrgDy = this.UserContext.LoadBizBillHeadDataById("bas_organization", saleOrgId, "fnumber");
                    if (saleOrgDy != null)
                    {
                        saleOrgId = Convert.ToString(saleOrgDy["fnumber"]);
                    }
                }
            }

            // orderDy
            return saleOrgId;
        }

        /// <summary>
        /// 获取直营售达方
        /// </summary>
        /// <param name="orderDy"></param>
        /// <returns></returns>
        private string GetDirectSaleToCOde(DynamicObject orderDy)
        {
            var saleToCodeStr = string.Empty;
            var billTypeId = Convert.ToString(orderDy["fbilltype"]);
            if (!billTypeId.IsNullOrEmptyOrWhiteSpace())
            {
                var billTypeDy = orderDy["fbilltype_ref"] as DynamicObject;
                if (billTypeDy == null)
                {
                    billTypeDy = this.UserContext.LoadBizBillHeadDataById("bd_billtype", billTypeId, "fname,fnumber");
                }

                var billTypeName = Convert.ToString(billTypeDy["fname"]);

                if (!billTypeName.IsNullOrEmptyOrWhiteSpace() && !billTypeName.EqualsIgnoreCase("门店上样"))
                {
                    var customerDy = orderDy["fcustomerid_ref"] as DynamicObject;

                    if (customerDy == null)
                    {
                        var customerId = Convert.ToString(orderDy["fcustomerid"]);
                        customerDy = this.UserContext.LoadBizBillHeadDataById("bas_customer", customerId, "fnumber");
                    }

                    if (customerDy != null)
                    {
                        saleToCodeStr = Convert.ToString(customerDy["fnumber"]);
                    }
                }
            }

            return saleToCodeStr;
        }

        /// <summary>
        /// 获取收款状态
        /// </summary>
        /// <param name="orderDy"></param>
        /// <returns></returns>
        private string GetPayStatus(DynamicObject orderDy)
        {
            string payStatus = "N";

            var receiptStatus = Convert.ToString(orderDy["freceiptstatus"]);

            // 传：Y / N 部分与未收款：传N
            switch (receiptStatus)
            {
                //全款未收
                case "receiptstatus_type_01":
                    payStatus = "N";
                    break;
                //部分收款
                case "receiptstatus_type_02":
                    payStatus = "N";
                    break;
                //全款已收
                case "receiptstatus_type_03":
                    payStatus = "Y";
                    break;
            }

            return payStatus;
        }

        /// <summary>
        /// 获取合作渠道的sap供应商编码
        /// </summary>
        /// <param name="orderDy"></param>
        /// <returns></returns>
        private string GetChannelSupplierCode(DynamicObject orderDy)
        {
            string supplierCode = string.Empty;
            var channelId = Convert.ToString(orderDy["fchannel"]);
            if (!channelId.IsNullOrEmptyOrWhiteSpace())
            {
                var channelDy = orderDy["fchannel_ref"] as DynamicObject;
                if (channelDy == null)
                {
                    channelDy = this.UserContext.LoadBizBillHeadDataById("ste_channel", channelId, "fsuppliercode");
                }

                if (channelDy != null)
                {
                    supplierCode = Convert.ToString(channelDy["fsuppliercode"]);
                }
            }

            return supplierCode;
        }

        /// <summary>
        /// 获取是否需要开票(是传1,不是就传0)
        /// </summary>
        /// <param name="orderDy"></param>
        /// <returns></returns>
        private string GetIsNeedInvoice(DynamicObject orderDy)
        {
            string isNeedInvoice = "0";
            isNeedInvoice = Convert.ToString(Convert.ToInt32(orderDy["fisinvoiceneed"]));
            return isNeedInvoice;
        }

        /// <summary>
        /// 获取门店自提
        /// </summary>
        /// <param name="productEntry">销售合同的商品明细行</param>
        /// <returns></returns>
        private string GetZyZmdzt(DynamicObject productEntry)
        {
            string zyZmdztStr = "N";

            var storeHouseId = Convert.ToString(productEntry["fstorehouseid"]);

            var isOutSpot = Convert.ToBoolean(Convert.ToInt32(productEntry["fisoutspot"]));

            if (isOutSpot)
            {
                zyZmdztStr = "Y";
            }

            /*if (storeHouseId.IsNullOrEmptyOrWhiteSpace())
            {
                return zyZmdztStr;
            }
            var storeHouseDy = productEntry["fstorehouseid_ref"] as DynamicObject;
            if (storeHouseDy == null)
            {
                storeHouseDy = this.UserContext.LoadBizBillHeadDataById("ydj_storehouse", storeHouseId, "fnumber,fname,fwarehousetype");
            }
            if (storeHouseDy == null)
            {
                return zyZmdztStr;
            }
            // 出门店仓现货传Y
            var wareHouseType = Convert.ToString(storeHouseDy["fwarehousetype"]);
            //warehouse_01 总仓 warehouse_02 门店仓
            if (wareHouseType.Equals("warehouse_01"))
            {
                zyZmdztStr = "Y";
            }
            else
            {
                zyZmdztStr = "N";
            }*/

            return zyZmdztStr;
        }

        /// <summary>
        /// 获取销售合同上的关联合同号
        /// </summary>
        /// <param name="orderDy"></param>
        /// <returns></returns>
        private string GetRelevanceOrderNo(DynamicObject orderDy)
        {
            //关联合同号
            var relevanceOrderNo = Convert.ToString(orderDy["frelevanceorderno"]);
            if (relevanceOrderNo.IsNullOrEmptyOrWhiteSpace()) return string.Empty;
            var relevanceOrderDy = orderDy["frelevanceorderno_ref"] as DynamicObject;
            if (relevanceOrderDy == null)
            {
                relevanceOrderDy = this.UserContext.LoadBizBillHeadDataById("ydj_order", relevanceOrderNo, "fbillno");
            }

            if (relevanceOrderDy == null)
            {
                relevanceOrderNo = string.Empty;
            }
            else
            {
                relevanceOrderNo = Convert.ToString(relevanceOrderDy["fbillno"]);
            }

            return relevanceOrderNo;
        }

        /// <summary>
        /// 获取折扣率
        /// </summary>
        /// <param name="orderDy"></param>
        /// <param name="productEntry"></param>
        /// <returns></returns>
        private object GetDiscPct(DynamicObject orderDy, DynamicObject productEntry)
        {
            var discPct = 10M;
            var orgId = Convert.ToString(orderDy["fmainorgid"]);
            var agentDy = this.UserContext.LoadBizBillHeadDataById("bas_agent", orgId, "fmanagemodel");
            if (agentDy != null)
            {
                var manageModel = Convert.ToString(agentDy["fmanagemodel"]);
                if (manageModel.EqualsIgnoreCase("1"))
                {
                    discPct = (10M - Convert.ToDecimal(productEntry["fdistrate"])) * 10;
                    if (discPct <= 0.00M)
                    {
                        discPct = 0.00M;
                    }
                }
                else
                {
                    discPct = Convert.ToDecimal(productEntry["fdistrate"]);
                }
            }
            else
            {
                discPct = Convert.ToDecimal(productEntry["fdistrate"]);
            }

            return discPct;
        }

        /// <summary>
        /// 直营计算单个商品的折扣金额(兼容直营以及非直营(通过消息队列同步的))
        /// </summary>
        /// <param name="orderDy"></param>
        /// <param name="productEntry"></param>
        /// <returns></returns>
        private object GetDisOnePrice(DynamicObject orderDy, DynamicObject productEntry)
        {
            var returnDisAmount = 0.00M;
            var orgId = Convert.ToString(orderDy["fmainorgid"]);
            var agentDy = this.UserContext.LoadBizBillHeadDataById("bas_agent", orgId, "fmanagemodel");
            if (agentDy != null)
            {
                var manageModel = Convert.ToString(agentDy["fmanagemodel"]);
                if (!manageModel.IsNullOrEmptyOrWhiteSpace())
                {
                    if (manageModel.EqualsIgnoreCase("1"))
                    {
                        var orderbyreturn = Convert.ToBoolean(Convert.ToInt32(orderDy["forderbyreturn"]));
                        if (orderbyreturn)
                        {
                            var distAmount = Convert.ToDecimal(productEntry["fdistamount"]);
                            return -distAmount;
                        }
                        else
                        {

                            var distAmount = Convert.ToDecimal(productEntry["fdistamount"]);
                            //行成交单件(使用保留二位小数的成交单价)
                            var dealPrice = Convert.ToDecimal(productEntry["rounded_fdealprice"]);
                            //行零售单价
                            var productPrice = Convert.ToDecimal(productEntry["fprice"]);

                            //销售数量
                            var bizQty = Convert.ToDecimal(productEntry["fbizqty"]);

                            if (bizQty <= 0.0M)
                            {
                                return 0.00M;
                            }

                            if ((productPrice - dealPrice) == 0.00M)
                            {
                                return 0.00M;
                            }

                            return -(productPrice - dealPrice);
                            /*var bizQty = Convert.ToDecimal(productEntry["fbizqty"]);
                            if (distAmount <= 0.00M || bizQty <= 0.00M)
                            {
                                returnDisAmount = 0.00M;
                            }
                            else
                            {
                                returnDisAmount = -(distAmount / bizQty);
                            }*/
                        }
                    }
                }
                else
                {
                    var distAmount = Convert.ToDecimal(productEntry["fdistamount"]);
                    returnDisAmount = distAmount;
                }
            }
            else
            {
                var distAmount = Convert.ToDecimal(productEntry["fdistamount"]);
                returnDisAmount = distAmount;
            }

            return returnDisAmount;
        }

        /// <summary>
        /// 设置传过去的Type的值
        /// </summary>
        /// <param name="e"></param>
        /// <param name="orderDy"></param>
        private void SetDataEntityDirectOrdeType(BeforeFieldMappingEventArgs e, DynamicObject orderDy)
        {
            var orgId = Convert.ToString(orderDy["fmainorgid"]);
            var agentDy = this.UserContext.LoadBizBillHeadDataById("bas_agent", orgId, "fmanagemodel");
            if (agentDy != null)
            {
                var manageModel = Convert.ToString(agentDy["fmanagemodel"]);
                if (!manageModel.IsNullOrEmptyOrWhiteSpace())
                {
                    if (manageModel.EqualsIgnoreCase("1"))
                    {
                        e.Cancel = true;
                        e.Result = "11";
                    }
                }
            }
        }

        /// <summary>
        /// 设置传过去的ZyProdType的值
        /// 产品定制类型(01:标准品、02:标准定制、03:非标定制)
        /// </summary>
        /// <param name="e"></param>
        /// <param name="productEntry"></param>
        private void SetRequestDataZyProdTypeValue(BeforeFieldMappingEventArgs e, DynamicObject productEntry)
        {
            var orgId = Convert.ToString(e.DataEntity["fmainorgid"]);
            var agentDy = this.UserContext.LoadBizBillHeadDataById("bas_agent", orgId, "fmanagemodel");
            if (agentDy != null)
            {
                var manageModel = Convert.ToString(agentDy["fmanagemodel"]);
                if (!manageModel.IsNullOrEmptyOrWhiteSpace())
                {
                    if (manageModel.EqualsIgnoreCase("1"))
                    {
                        e.Cancel = true;
                        //非标
                        var unStdType = Convert.ToBoolean(Convert.ToInt32(productEntry["funstdtype"]));

                        //辅助属性
                        var attrInfo = Convert.ToString(productEntry["fattrinfo"]);

                        //标准品
                        if (!unStdType && attrInfo.IsNullOrEmptyOrWhiteSpace())
                        {
                            e.Result = "01";

                            return;
                        }

                        //标准定制
                        if (!unStdType && !attrInfo.IsNullOrEmptyOrWhiteSpace())
                        {
                            e.Result = "02";
                            return;
                        }

                        //非标定制
                        if (unStdType && !attrInfo.IsNullOrEmptyOrWhiteSpace())
                        {
                            e.Result = "03";
                            return;
                        }

                    }
                }
            }
        }

        private void SetRequestDataCrtName(BeforeFieldMappingEventArgs e, DynamicObject orderDy)
        {
            var orgId = Convert.ToString(e.DataEntity["fmainorgid"]);
            var agentDy = this.UserContext.LoadBizBillHeadDataById("bas_agent", orgId, "fmanagemodel");
            if (agentDy != null)
            {
                var manageModel = Convert.ToString(agentDy["fmanagemodel"]);
                if (!manageModel.IsNullOrEmptyOrWhiteSpace())
                {
                    if (manageModel.EqualsIgnoreCase("1"))
                    {
                        e.Cancel = true;
                        var staffDy = orderDy["fstaffid_ref"] as DynamicObject;
                        if (staffDy == null)
                        {
                            var staffId = Convert.ToString(orderDy["fstaffid"]);
                            staffDy = this.UserContext.LoadBizBillHeadDataById("bd_staff", staffId, "fname,fphone");
                        }

                        var staffName = Convert.ToString(staffDy["fname"]);

                        var staffPhone = Convert.ToString(staffDy["fphone"]);

                        e.Result = staffName + staffPhone;
                    }
                }
            }
        }

        private string GetDeliver(DynamicObject item)
        {
            bool IsRenewalPur = Convert.ToBoolean(item["frenewalflag"]);
            if (!IsRenewalPur) return "";
            string fdeliverid = "";
            //得到城市
            var cityid = GetStoreCity(item["fdeptid"].ToString());

            //明细里找出一个业绩品牌
            var fentries = item["fentry"] as DynamicObjectCollection;

            //先从明细行找，如果有送达方直接做明细行的
            var fsaleorgid = Convert.ToString(fentries.FirstOrDefault()["fshipperdeliver"]).Trim();
            if (fsaleorgid.IsNullOrEmptyOrWhiteSpace())
            {
                //业绩品牌做了分组下推多个都是一组的，默认取一条
                var fresultbrandid = fentries.FirstOrDefault()["fresultbrandid"].ToString();
                var dm = GetDeliverByBrandidAndCity(fresultbrandid, cityid, IsRenewalPur);
                if (dm != null)
                {
                    //送达方赋值
                    fdeliverid = dm["id"].ToString();
                }
                else if (this.UserContext.IsSecondOrg)
                {
                    var _data = GetDefaultDeliver();
                    fdeliverid = _data?["id"].ToString();
                }
            }
            else
            {
                fdeliverid = fsaleorgid;
            }
            if (!string.IsNullOrWhiteSpace(fdeliverid))
            {
                var deliverObj = this.UserContext.LoadBizDataById("bas_deliver", fdeliverid);
                if (deliverObj != null)
                {
                    return Convert.ToString(deliverObj["fnumber"]);
                }
            }
            return fdeliverid;
        }


        /// <summary>
        /// 根据城市和品牌找到送达方
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns></returns>
        private DynamicObject GetDeliverByBrandidAndCity(string fresultbrandid, string fcity, bool IsRenewalPur)
        {
            if (fresultbrandid.IsNullOrEmptyOrWhiteSpace() || fcity.IsNullOrEmptyOrWhiteSpace())
                return null;
            var metaModelService = this.UserContext.Container.GetService<IMetaModelService>();
            var htmlForm = metaModelService.LoadFormModel(UserContext, "bas_deliver");
            var dm = this.UserContext.Container.GetService<IDataManager>();
            dm.InitDbContext(this.UserContext, htmlForm.GetDynamicObjectType(this.UserContext));
            var Agents = new List<string>() { UserContext.Company };
            //获取当前用户登录经销商的《主经销商配置表》
            var topCtx = UserContext.CreateTopOrgDBContext();
            var mainAgentConfigs = topCtx.LoadBizDataByACLFilter("bas_mainagentconfig", $" fmainagentid = '{UserContext.Company}'  AND fforbidstatus='0' ").FirstOrDefault();
            if (mainAgentConfigs != null)
            {
                //存在配置，则需要将所有子经销商也包含进来
                var subAgentEntrys = mainAgentConfigs["fsubagent"] as DynamicObjectCollection;
                if (subAgentEntrys != null && subAgentEntrys.Any())
                {
                    Agents.AddRange(subAgentEntrys.Select(t => Convert.ToString(t["fsubagentid"])));
                }
            }

            var where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}'".Fmt(string.Join("','", Agents), fcity);

            var sqlParam = new List<SqlParam>
            {
            };
            var reader = UserContext.GetPkIdDataReader(htmlForm, where, sqlParam);
            var data = dm.SelectBy(reader).OfType<DynamicObject>();
            if (data != null && data.Count() > 0)
            {
                //35321 【慕思现场-正式区问题 526】调整 销售转采购 送达方生成逻辑, 如果匹配到多个送达方时, 系统不进行赋值让用户手动选择
                var count = data.Where(c => (c["fentry"] as DynamicObjectCollection).Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]))).Count();
                if ((!IsRenewalPur && count == 1) || (IsRenewalPur && count >= 1))
                {
                    foreach (var item in data)
                    {
                        var fentry = item["fentry"] as DynamicObjectCollection;
                        var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                        if (isexists)
                            return item;
                    }
                }
                //如果城市 +系列没匹配到 还是要走上级城市逻辑
                if (count == 0)
                {
                    var parentcity = this.UserContext.Container.GetService<IOrderService>().GetParentCity(this.UserContext, fcity);
                    if (parentcity.IsNullOrEmptyOrWhiteSpace()) return null;

                    where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}' ".Fmt(string.Join("','", Agents), parentcity);
                    var deliver = this.UserContext.LoadBizDataByFilter("bas_deliver", where);
                    if (!deliver.IsNullOrEmptyOrWhiteSpace())
                    {
                        var count_temp = deliver.Where(c => (c["fentry"] as DynamicObjectCollection).Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]))).Count();
                        if ((!IsRenewalPur && count_temp == 1) || (IsRenewalPur && count_temp >= 1))
                        {
                            foreach (var item in deliver)
                            {
                                var fentry = item["fentry"] as DynamicObjectCollection;
                                var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                                if (isexists)
                                    return item;
                            }
                        }
                    }
                }
            }
            //通过深圳市宝安区匹配不到，尝试通过深圳市匹配
            else
            {
                var parentcity = this.UserContext.Container.GetService<IOrderService>().GetParentCity(this.UserContext, fcity);

                if (parentcity.IsNullOrEmptyOrWhiteSpace()) return null;

                where = @"fforbidstatus = 0 and fagentid in ('{0}') and fcity='{1}' ".Fmt(string.Join("','", Agents), parentcity);
                var deliver = this.UserContext.LoadBizDataByFilter("bas_deliver", where);
                if (!deliver.IsNullOrEmptyOrWhiteSpace())
                {
                    var count = deliver.Where(c => (c["fentry"] as DynamicObjectCollection).Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]))).Count();
                    if ((!IsRenewalPur && count == 1) || (IsRenewalPur && count >= 1))
                    {
                        foreach (var item in deliver)
                        {
                            var fentry = item["fentry"] as DynamicObjectCollection;
                            var isexists = fentry.Any(x => x["fserieid"].ToString() == fresultbrandid && Convert.ToBoolean(x["fenable"]));
                            if (isexists)
                                return item;
                        }
                    }
                }
            }
            return null;
        }

        private DynamicObject GetDefaultDeliver()
        {
            string orgid = this.UserContext.IsTopOrg ? this.UserContext.TopCompanyId : UserContext.Company;
            return this.UserContext.LoadBizDataByFilter("bas_deliver", $" fforbidstatus = 0 and fagentid = '{orgid}' ").FirstOrDefault();
        }

        /// <summary>
        /// 得到门店城市
        /// </summary>
        /// <param name="deptid"></param>
        /// <returns></returns>
        private string GetStoreCity(string deptid)
        {
            string strSql = @"select top 1 t2.fmycity from t_bd_department t1 with(nolock)
                            join t_bas_store t2 with(nolock) on t1.fstore = t2.fid where t1.fid =@fid";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fid", System.Data.DbType.String, deptid)
            };
            string fmycity = "";
            using (var dr = this.UserContext.ExecuteReader(strSql, sqlParam))
            {
                if (dr.Read())
                {
                    fmycity = Convert.ToString(dr["fmycity"]);
                }
            }

            return fmycity;
        }

        /// <summary>
        /// 佣金
        /// </summary>
        /// <param name="productEntry"></param>
        /// <returns></returns>
        private string GetZyKbetrzk91(DynamicObject productEntry)
        {
            return Convert.ToString(productEntry["fcommissionamount_e"]);
        }

        /// <summary>
        /// 设置沙发组合最小行号
        /// </summary>
        /// <param name="e"></param>
        /// <param name="dataEntity">商品明细行</param>
        /// <param name="bizEntity">销售合同数据包</param>
        private void SetSofaMiniNum(BeforeFieldMappingEventArgs e, DynamicObject dataEntity, DynamicObject bizEntity)
        {
            // 【沙发组合号】一样的行, 对应找到最小的行序号
            e.Cancel = true;
            e.Result = "";

            string sofaCombNumber = Convert.ToString(dataEntity["fsofacombnumber"]);

            if (!sofaCombNumber.IsNullOrEmptyOrWhiteSpace())
            {
                var entries = (DynamicObjectCollection)bizEntity["fentry"];
                var minSeq = entries
                    .Where(x => Convert.ToString(x["fsofacombnumber"]) == sofaCombNumber)
                    .Select(x => Convert.ToInt32(x["fseq"]))
                    .Min();

                e.Result = Convert.ToString(minSeq);
            }
        }
        
        /// <summary>
        /// 设置套件父行号
        /// </summary>
        /// <param name="e"></param>
        /// <param name="dataEntity"></param>
        /// <param name="bizEntity"></param>
        private void SetSuitPariNum(BeforeFieldMappingEventArgs e, DynamicObject dataEntity, DynamicObject bizEntity)
        {
            // 根据《采购订单》单据体.商品明细行的【是否套件】+【套件组合号】来判断, 如果商品行有【套件组合号】且【是否套件】为否的, 都需要赋值套件父行号, 赋值逻辑为对应的【套件组合号】 且 有勾选上【是否套件】的行号, 即为套件父行号
            e.Cancel = true;
            e.Result = "";

            var productObj = dataEntity["fproductid_ref"] as DynamicObject;
            if (productObj == null)
                dataEntity["fproductid_ref"] = productObj =
                    this.UserContext.LoadBizDataById("ydj_product", Convert.ToString(dataEntity["fproductid"]));

            bool isSuitFlag = Convert.ToBoolean(productObj?["fsuiteflag"]);
            string suitCombNumber = Convert.ToString(dataEntity["fsuitcombnumber"]);

            if (!suitCombNumber.IsNullOrEmptyOrWhiteSpace() && !isSuitFlag)
            {
                var entries = (DynamicObjectCollection)bizEntity["fentry"];

                foreach (var entry in entries)
                {
                    if (Convert.ToString(entry["fsuitcombnumber"]).EqualsIgnoreCase(suitCombNumber) &&
                        Convert.ToBoolean((entry["fproductid_ref"] as DynamicObject)?["fsuiteflag"]))
                    {
                        e.Result = Convert.ToString(entry["fseq"]);
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 设置直发订单的套件头行号或者沙发最小行号(不用了)
        /// </summary>
        /// <param name="e"></param>
        /// <param name="dataEntity"></param>
        /// <param name="bizEntity"></param>
        public void SetDirectOrderSofaMiniNum(BeforeFieldMappingEventArgs e,DynamicObject dataEntity,DynamicObject bizEntity)
        {
            string sofaMiniNumStr = string.Empty;
            var orgId = Convert.ToString(bizEntity["fmainorgid"]);
            var sofaCombNumber = Convert.ToString(dataEntity["fsofacombnumber"]);
            var suitCombNumber = Convert.ToString(dataEntity["fsuitcombnumber"]);
            if (!sofaCombNumber.IsNullOrEmptyOrWhiteSpace())
            {
                SetSofaMiniNum(e, dataEntity, bizEntity);
            }

            if (!suitCombNumber.IsNullOrEmptyOrWhiteSpace())
            {
                SetSuitPariNum(e, dataEntity, bizEntity);
            }
        }

        /// <summary>
        /// 设置经销商经营模式字典的信息
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="orderDys"></param>
        public void SetAgentManageModelDic(UserContext userCtx,IEnumerable<DynamicObject> orderDys)
        {
            if(orderDys == null || !orderDys.Any()) return;

            var orgIdList = orderDys.Select(x=>Convert.ToString(x["fmainorgid"])).ToList();

            var agentDys = userCtx.LoadBizBillHeadDataById("bas_agent", orgIdList,"fmanagemodel");

            if (agentDys != null && agentDys.Any())
            {
                if (AgentManageModelDic == null)
                {
                    AgentManageModelDic = new ConcurrentDictionary<string, bool>();
                }
                foreach (var agentDy in agentDys)
                {
                    var agentId = Convert.ToString(agentDy["id"]);
                    var isDirect = Convert.ToBoolean(Convert.ToInt32(agentDy["fmanagemodel"]));
                    if (!AgentManageModelDic.ContainsKey(agentId))
                    {
                        AgentManageModelDic.TryAdd(agentId, isDirect);
                    }
                }
            }
        }

        /// <summary>
        /// 当前数据包是否为直营数据包
        /// </summary>
        /// <param name="orderDy"></param>
        /// <returns></returns>
        public bool IsDirectSaleManageModel(DynamicObject orderDy)
        {
            bool isTrue = false;

            var orgId = Convert.ToString(orderDy["fmainorgid"]);

            if (AgentManageModelDic.ContainsKey(orgId))
            {
                var isDirectSaleTrue = AgentManageModelDic[orgId];
                isTrue = isDirectSaleTrue;
            }

            return isTrue;
        }

        /// <summary>
        /// 中台合同附件模型
        /// </summary>
        public class OrderImgModel
        {
            public string code { get; set; }
            public string name { get; set; }
            public string fileAddr { get; set; }
        }
    }
}