<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_purchaseorder" basemodel="bill_basetmpl" el="1" cn="采购订单" approvalflow="true" ubl="1" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_purchaseorder" pn="fbillhead" cn="采购订单">

        <!--重写基类模型中的部分字段属性-->
        <input lix="1" id="fbillno" el="108" visible="-1" width="115" align="center" frozen="1" />
        <input lix="10" id="fdescription" el="100" visible="-1" len="1000" canchange="true" uaul="true" />
        <input lix="11" id="fsourcetype" el="140" visible="-1" />
        <input lix="12" id="fsourcenumber" el="141" visible="-1" />
        <select lix="13" id="fstatus" el="122" visible="-1"></select>
        <select el="152" ek="fbillhead" visible="-1" id="fchangestatus" fn="fchangestatus" pn="fchangestatus" cn="变更状态"
                vals="'0':'正常','1':'变更中','2':'变更完成','3':'变更已提交'" xlsin="0" copy="0" lix="266" align="center" lock="-1"></select>
        <select el="160" ek="fbillhead" visible="-1" id="fclosestatus" fn="fclosestatus" pn="fclosestatus" cn="关闭状态" defval="'0'" xlsin="0" copy="0" lix="270" lock="-1"></select>
        <input type="text" group="基本信息" id="fcreatorid" el="118" ek="fbillhead" refId="Sec_User" dfld="FName" fn="fcreatorid" pn="fcreatorid" cn="创建人" visible="-1" copy="0" xlsin="0" lix="1" />

        <!--基本信息-->
        <input lix="2" group="基本信息" el="123" ek="fbillhead" visible="-1" id="fbilltypeid" fn="fbilltypeid" pn="fbilltypeid" refid="bd_billtype" cn="单据类型" copy="0" must="1" />
        <input lix="3" group="基本信息" el="112" ek="fbillhead" visible="-1" id="fdate" fn="fdate" ts="" cn="订单日期" defval="@currentshortdate" copy="0" width="90" canchange="true" must="1" />
        <input lix="4" group="基本信息" el="106" ek="fbillhead" visible="-1" id="fsupplierid" fn="fsupplierid" ts="" cn="供应商" refid="ydj_supplier" width="85" dfld="fname,fcontacts,fphone,faddress,fbank,fbanknumber,fcommontaxrate,ftaxpayernumber,forgid" must="1" />
        <input lix="4" group="基本信息" el="107" ek="fbillhead" visible="0" id="forgid" fn="forgid" pn="forgid" cn="销售组织" lock="-1" copy="1" notrace="true" ts="" dfld="fmycity,fname" ctlfk="fsupplierid" dispfk="forgid" />

        <input lix="5" group="财务信息" el="105" id="ffbillamount" ek="fbillhead" fn="ffbillamount" ts="" visible="-1" cn="成交金额" copy="0" width="90" canchange="true" />
        <input lix="6" group="基本信息" el="106" ek="fbillhead" id="fpodeptid" fn="fpodeptid" pn="fpodeptid" cn="采购部门" visible="-1" refid="ydj_dept" canchange="true" />
        <input group="基本信息" el="107" ek="fbillhead" visible="0" id="fstore" fn="fstore" pn="fstore" cn="门店" lock="-1" copy="1" notrace="true" ts="" dfld="fmycity,fname" ctlfk="fpodeptid" dispfk="fstore" lix="9" />

        <input lix="7" group="基本信息" el="106" ek="fbillhead" id="fpostaffid" fn="fpostaffid" pn="fpostaffid" cn="采购员" visible="-1" refid="ydj_staff" defVal="@currentStaffId" must="2" canchange="true" />
        <input lix="8" group="基本信息" el="112" ek="fbillhead" visible="-1" id="fpickdate" fn="fpickdate" ts="" cn="交货日期" defval="@currentshortdate" copy="0" width="105" canchange="true" must="1" />

        <input lix="20" group="财务信息" el="105" ek="fbillhead" id="fdistamount" fn="fdistamount" pn="fdistamount" cn="折扣额" copy="0" width="90" visible="1150" format="0,000.00" lock="-1" />
        <input lix="20" group="基本信息" el="112" ek="fbillhead" visible="1150" id="fdeliveryplandate" fn="fdeliveryplandate" ts="" cn="供方预计交货日期" copy="0" width="105" canchange="true" lock="0" />
        <input lix="40" group="基本信息" el="107" ek="fbillhead" visible="1150" id="fcustomernumber" fn="fcustomernumber" pn="fcustomernumber" cn="客户编码" ctlfk="fcustomerid" dispfk="fnumber" />
        <input lix="40" group="基本信息" el="106" ek="fbillhead" visible="-1" id="fcustomerid" fn="fcustomerid" pn="fcustomerid" cn="客户" lock="-1" refid="ydj_customer" copy="0" width="85" canchange="true" />
        <input lix="40" group="基本信息" el="100" ek="fbillhead" visible="1150" id="fconsignee" fn="fconsignee" pn="fconsignee" cn="收货人" lock="-1" />
        <input lix="45" group="基本信息" el="100" ek="fbillhead" visible="1150" id="fphone" fn="fphone" pn="fphone" cn="手机号" lock="-1" width="80" copy="0" canchange="true" notrace="false" />
        <input lix="80" group="基本信息" el="122" ek="fbillhead" visible="1150" id="fprovince" fn="fprovince" pn="fprovince" cn="省" cg="省" lock="-1" refid="bd_enum" dfld="fenumitem" copy="0" canchange="true" />
        <input lix="80" group="基本信息" el="122" ek="fbillhead" visible="1150" id="fcity" fn="fcity" pn="fcity" cn="市" cg="市" lock="-1" refid="bd_enum" dfld="fenumitem" copy="0" canchange="true" />
        <input lix="80" group="基本信息" el="122" ek="fbillhead" visible="1150" id="fregion" fn="fregion" pn="fregion" cn="区" cg="区" lock="-1" refid="bd_enum" dfld="fenumitem" copy="0" canchange="true" />
        <input lix="80" group="基本信息" el="100" ek="fbillhead" visible="1150" id="faddress" fn="faddress" pn="faddress" cn="详细地址" lock="-1" copy="0" canchange="true" />

        <input lix="80" group="基本信息" el="122" ek="fbillhead" visible="0" gt="2" id="fprovince_gb" fn="fprovince_gb" pn="fprovince_gb" cn="国补省" cg="省" lock="-1" refid="bd_enum" dfld="fenumitem" copy="0" />
        <input lix="80" group="基本信息" el="122" ek="fbillhead" visible="0" gt="2" id="fcity_gb" fn="fcity_gb" pn="fcity_gb" cn="国补市" cg="市" lock="-1" refid="bd_enum" dfld="fenumitem" copy="0" />
        <input lix="80" group="基本信息" el="122" ek="fbillhead" visible="0" gt="2" id="fregion_gb" fn="fregion_gb" pn="fregion_gb" cn="国补区" cg="区" lock="-1" refid="bd_enum" dfld="fenumitem" copy="0" />
        <input lix="80" group="基本信息" el="100" ek="fbillhead" visible="0" id="faddress_gb" fn="faddress_gb" pn="faddress_gb" cn="国补详细地址" lock="-1" copy="0" />

        <input lix="65" group="基本信息" el="108" ek="fbillhead" visible="1150" id="forderno" fn="forderno" pn="forderno" cn="销售合同编号" lock="-1" copy="0" notrace="false" ts="" />

        <select lix="20" group="基本信息" el="122" ek="fbillhead" visible="1150" id="ftype" fn="ftype" pn="ftype" cn="业务类型" cg="业务类型" refid="bd_enum" dfld="fenumitem" defval="'order_type_01'" width="90" apipn="type" must="1"></select>
        <input el="107" ek="fbillhead" visible="0" id="fsupplierstatus" fn="fsupplierstatus" cn="供应商协同状态" ctlfk="fsupplierid" dispfk="fcoostate" copy="0" />
        <input lix="30" group="基本信息" el="100" ek="fbillhead" id="fsupplieraddr" fn="fsupplieraddr" ts="" cn="供方地址" visible="1150" canchange="true" />
        <input lix="30" group="基本信息" el="116" ek="fbillhead" id="fenablenotice" fn="fenablenotice" pn="fenablenotice" visible="0" cn="启用通知"
               lock="0" copy="1" notrace="false" ts="" defval="false" canchange="true" />
        <input lix="20" group="基本信息" el="122" ek="fbillhead" visible="0" id="fmainproduct" fn="fmainproduct" pn="fmainproduct" cn="主产品"
               cg="主产品" refid="bd_enum" defval="" width="80" apipn="mainProduct" />
        <input group="基本信息" el="100" ek="fbillhead" visible="-1" id="fmemberdesc" fn="fmemberdesc" pn="fmemberdesc" cn="未注册会员原因" len="1000" width="100" />

        <input group="基本信息" el="116" ek="fbillhead" id="fneedtransferorder" fn="fneedtransferorder" pn="fneedtransferorder" visible="1150" cn="需转单"
               copy="1" lix="0" notrace="false" ts="" defval="false" canchange="true" lock="-1" />

        <select group="基本信息" el="122" ek="fbillhead" visible="0" id="fdeliverytype" fn="fdeliverytype" pn="fdeliverytype" cn="交货方式" cg="交货方式" refid="bd_enum" dfld="fenumitem"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="0" id="finstallationtype" fn="finstallationtype" pn="finstallationtype" cn="安装方式" cg="安装方式" refid="bd_enum" dfld="fenumitem"></select>
        <select group="基本信息" el="122" ek="fbillhead" visible="0" id="fchanneltype" fn="fchanneltype" pn="fchanneltype" cn="渠道形式" cg="渠道形式" refid="bd_enum" dfld="fenumitem"></select>
        <input group="基本信息" el="100" ek="fbillhead" id="finvoice" fn="finvoice" pn="finvoice" cn="开票信息" visible="1150" />

        <input group="基本信息" el="116" ek="fbillhead" id="fiswaterredlinefail" fn="fiswaterredlinefail" pn="fiswaterredlinefail" visible="1150" cn="水位线红灯提交失败状态"
               copy="0" lix="0" notrace="false" ts="" defval="false" lock="-1" />
        <input group="基本信息" el="122" ek="fbillhead" id="fstockwaterspecialstatus" fn="fstockwaterspecialstatus" pn="fstockwaterspecialstatus" visible="1150" cn="库存水位管控特批状态"
               cg="库存水位管控特批状态" refid="bd_enum" dfld="fenumitem" lix="0" notrace="false" ts="" copy="0" lock="-1" />
        <!-- 协同信息 -->
        <input lix="80" group="协同信息" el="100" ek="fbillhead" visible="32" id="fsupplierorderno" fn="fsupplierorderno" pn="fsupplierorderno" cn="供货方订单号" width="80" copy="0" />
        <input lix="80" group="协同信息" el="100" ek="fbillhead" visible="32" id="fsupplieroldorderno" fn="fsupplieroldorderno" pn="fsupplieroldorderno" cn="供货方正单单号" width="80" copy="0" />
        <input lix="80" group="协同信息" el="107" ek="fbillhead" visible="1150" id="fdeptnumber" fn="fdeptnumber" pn="fdeptnumber" cn="部门编码" ctlfk="fdeptid" dispfk="fnumber" />
        <input lix="80" group="协同信息" el="106" ek="fbillhead" visible="32" id="fdeptid" fn="fdeptid" pn="fdeptid" cn="销售部门" refid="ydj_dept" reflvt="1" width="140" canchange="true" />
        <input lix="80" group="协同信息" el="106" ek="fbillhead" visible="32" id="fstaffid" fn="fstaffid" pn="fstaffid" cn="销售员" refid="ydj_staff" canchange="true" />
        <input lix="80" group="协同信息" el="106" ek="fbillhead" visible="32" id="fstylistid" fn="fstylistid" pn="fstylistid" cn="设计师" refid="ydj_staff" canchange="true" />
        <input lix="80" group="协同信息" el="402" ek="fbillhead" visible="1150" id="fquotelist" fn="fquotelist" cn="报价清单" ctlfk="fsupplierid" copy="0" lock="0" acl="1" />
        <input lix="80" group="协同信息" el="152" type="text" ek="fbillhead" id="fchargebackstatus" fn="fchargebackstatus" cn="退单状态" vals="'0':'正常','1':'已退单','2':'退单已重发'" visible="1150" defval="'0'" lock="-1" />
        <input lix="80" group="协同信息" el="100" type="text" ek="fbillhead" id="fchargebackreason" fn="fchargebackreason" cn="退单原因" visible="1150" lock="-1" />
        <input lix="85" group="协同信息" el="122" ek="fbillhead" visible="1150" id="fsource" fn="fsource" pn="fsource" cn="客户来源" cg="客户来源" refid="bd_enum" dfld="fenumitem" />
        <!--财务信息-->
        <input lix="55" group="财务信息" el="105" id="fdealamount" ek="fbillhead" fn="fdealamount" ts="" visible="1150" cn="货品原值" lock="-1" copy="0" width="90" />
        <input lix="9" group="财务信息" el="122" ek="fbillhead" id="fpaystatus" fn="fpaystatus" refId="bd_enum" dfld="fenumitem" ts="" defval="'paystatus_type_01'" cg="付款状态" visible="-1" cn="结算状态" lock="-1" copy="0" width="90" align="center" />
        <input lix="60" group="财务信息" el="105" id="fpinvoicemount" ek="fbillhead" fn="fpinvoicemount" ts="" visible="0" desc="该字段已废弃" cn="开票金额" lock="-1" copy="0" />
        <input lix="60" group="财务信息" el="105" id="fsettleamount" ek="fbillhead" fn="fsettleamount" ts="" visible="32" cn="已付金额" lock="-1" copy="0" />
        <input lix="60" group="财务信息" el="105" id="fpayamount" ek="fbillhead" fn="fpayamount" ts="" visible="32" cn="待结算金额" lock="-1" copy="0" />
        <input lix="60" group="财务信息" el="105" id="fpaidamount" ek="fbillhead" fn="fpaidamount" ts="" visible="32" cn="已结算金额" lock="-1" copy="0" />
        <input lix="60" group="财务信息" el="105" id="fconfirmamount" ek="fbillhead" fn="fconfirmamount" ts="" visible="32" cn="待确认金额" lock="-1" copy="0" width="105" />
        <input lix="60" group="财务信息" el="105" ek="fbillhead" visible="1150" id="factrefundamount" fn="factrefundamount" cn="实退金额" lock="-1" />
        <input lix="60" group="财务信息" el="122" ek="fbillhead" id="fbizstatus" fn="fbizstatus" pn="fbizstatus" refid="bd_enum" cg="协同订单业务状态" dfld="fenumitem" defval="'business_status_01'" ts="" visible="1150" cn="协同状态" lock="-1" copy="0" width="90" align="center" />
        <input lix="60" group="财务信息" el="105" ek="fbillhead" id="FRefundAmount" fn="FRefundAmount" pn="FRefundAmount" visible="1150" cn="申请退货金额"
               lock="-1" copy="0" notrace="false" ts="" roundType="0" format="0,000.00" />
        <!--后台字段-->
        <input group="后台字段" el="122" ek="fbillhead" visible="0" id="fpublishstatus" fn="fpublishstatus" cn="发布状态" refid="bd_enum" dfld="fenumitem" cg="协同订单发布状态" defval="'publish_status_01'" copy="0" />
        <input group="后台字段" el="100" ek="fbillhead" visible="0" id="fpublishcompany" fn="fpublishcompany" cn="发布企业" copy="0" />
        <input group="后台字段" el="100" ek="fbillhead" visible="0" id="fpublishcompanyid" fn="fpublishcompanyid" cn="发布企业id" copy="0" />
        <input group="后台字段" el="113" ek="fbillhead" visible="0" id="fpublishdate" fn="fpublishdate" pn="fpublishdate" cn="发布时间" copy="0" />
        <input group="后台字段" el="100" ek="fbillhead" visible="0" id="forderstatus" fn="forderstatus" cn="订单状态" lock="-1" copy="0" desc="该字段为纯文本，由对方cloud系统更新" />

        <input lix="1000" group="后台字段" el="144" ek="fbillhead" visible="1150" id="foperate" fn="foperate" pn="foperate" cn="操作" width="180" />

        <input group="后台字段" el="103" ek="fbillhead" visible="0" id="fsuminqty" fn="fsuminqty" cn="基本单位总入库数量" lock="-1" copy="0" />
        <input group="后台字段" el="103" ek="fbillhead" visible="0" id="fbizsuminqty" fn="fbizsuminqty" cn="采购总入库数量" lock="-1" copy="0" />

        <input group="后台字段" el="100" ek="fbillhead" visible="0" id="fplatformorderid" fn="fplatformorderid" pn="fplatformorderid" cn="三维家订单的通用ID" lock="-1" copy="0" />

        <!--图纸信息-->
        <input group="图纸信息" id="fdrawtype" el="122" ek="fbillhead" fn="fdrawtype" refId="bd_enum" dfld="fenumitem" ts="" cg="图纸类型" visible="32" cn="图纸类型" copy="0" />

        <!-- 慕思扩展 -->
        <input lix="54" group="基本信息" el="106" ek="fbillhead" visible="1150" id="fdeliverid" fn="fdeliverid" pn="fdeliverid" cn="送达方" refid="bas_deliver" width="150" lock="0" />
        <input group="基本信息" el="113" ek="fbillhead" visible="-1" id="fhqderdate" fn="fhqderdate" pn="fhqderdate" cn="提交总部时间" copy="0" lock="-1" />
        <input lix="30" group="基本信息" el="152" ek="fbillhead" visible="1150" id="fhqderstatus" fn="fhqderstatus" pn="fhqderstatus" cn="总部合同状态" vals="'01':'新建','02':'提交至总部','03':'已终审','04':'排产中','05':'驳回'" defval="" copy="0" lock="-1" />
        <input lix="30" group="基本信息" el="152" ek="fbillhead" visible="0" id="fhqderchgstatus" fn="fhqderchgstatus" pn="fhqderchgstatus" cn="总部变更状态" vals="'01':'提交至总部','02':'关闭','03':'驳回'" defval="" copy="0" lock="-1" />
        <input lix="57" group="基本信息" el="100" ek="fbillhead" visible="1150" id="fhqderno" fn="fhqderno" pn="fhqderno" lock="-1" copy="0" cn="总部合同号" />
        <input lix="57" group="基本信息" el="113" ek="fbillhead" visible="1150" id="fhqderauditdate" fn="fhqderauditdate" pn="fhqderauditdate" lock="-1" copy="0" cn="总部终审时间" format="yyyy-MM-dd HH:mm:ss" />

        <input lix="57" group="基本信息" el="100" ek="fbillhead" visible="1150" id="fhqsyncdesc" fn="fhqsyncdesc" pn="fhqsyncdesc" lock="-1" copy="0" cn="总部同步信息" len="200" />
        <input lix="57" group="基本信息" el="100" ek="fbillhead" visible="1150" id="fhqdertype" fn="fhqdertype" pn="fhqdertype" lock="-1" copy="0" cn="总部合同类型" />
        <input el="106" ek="fbillhead" id="fcloseid" fn="fcloseid" pn="fcloseid" visible="0" refId="Sec_User" dfld="FName" cn="关闭人" xlsin="0" copy="0" lix="300" lock="-1" />

        <input type="datetime" id="fcreatedate" el="119" ek="fbillhead" fn="fcreatedate" pn="fcreatedate" cn="创建日期" width="130" visible="-1" copy="0" xlsin="0" lix="2" />

        <input lix="57" group="基本信息" el="100" ek="fbillhead" visible="1150" id="fonelvoderno" fn="fonelvoderno" pn="fonelvoderno" lock="-1" copy="0" cn="一级合同号" />
        <input lix="30" group="基本信息" el="152" ek="fbillhead" visible="1150" id="fonelvoderstatus" fn="fonelvoderstatus" pn="fonelvoderstatus" cn="一级合同状态" vals="'01':'提交至一级','02':'订单已审核','03':'订单已驳回','04':'订单已出库'" defval="" copy="0" lock="-1" />
        <!-- 三维家 -->

        <input lix="59" group="基本信息" el="152" ek="fbillhead" visible="1150" id="fsendtarget" fn="fsendtarget" pn="fsendtarget" cn="直发标记" vals="'0':'门店','1':'消费者'" />
        <input lix="65" group="基本信息" el="100" ek="fbillhead" visible="1150" id="fthirdsource" fn="fthirdsource" pn="fthirdsource" cn="第三方来源" copy="0" />
        <input lix="70" group="基本信息" el="100" ek="fbillhead" visible="1150" id="fthirdbillno" fn="fthirdbillno" pn="fthirdbillno" cn="第三方单号" copy="0" />
        <input lix="75" group="基本信息" el="100" ek="fbillhead" visible="1150" id="fconsumersaddress" fn="fconsumersaddress" pn="fconsumersaddress" cn="消费者地址" copy="0" />
        <input lix="80" group="基本信息" el="116" ek="fbillhead" visible="36" id="fomsservice" fn="fomsservice" pn="fomsservice" cn="启用定制OMS" copy="0" />

        <input lix="76" group="基本信息" el="116" ek="fbillhead" visible="1150" id="fswjorderstate" fn="fswjorderstate" pn="fswjorderstate" cn="三维家门店审核" copy="0" lock="-1" />
        <input group="基本信息" el="100" ek="fbillhead" visible="0" lock="-1" id="fswjdetailurl" fn="fswjdetailurl" pn="fswjdetailurl" cn="三维家详情页跳转链接" copy="0" />
        <input lix="65" group="基本信息" el="100" ek="fbillhead" visible="1150" id="fswjordernumber" fn="fswjordernumber" pn="fswjordernumber" cn="三维家单号" copy="0" lock="-1" />
        <select group="基本信息" el="152" ek="fbillhead" visible="1150" id="findenttype" fn="findenttype" pn="findenttype" cn="订单类型"
                vals="'L':'零售单','H':'售后单','G':'工程单','Z':'增补单','C':'成品单','S':'试样单','Y':'样品单'" defval="'L'" lix="80" width="90" apipn="indenttype" lock="-1"></select>
        <input group="基本信息" el="152" ek="fbillhead" visible="0" id="forderattr" fn="forderattr" pn="forderattr" cn="订单属性" lock="-1" copy="0" width="100" vals="'1':'散单','2':'整家宅配','3':'大家居'" lix="200" />
        <input group="基本信息" el="100" ek="fbillhead" visible="0" id="factivenumber" fn="factivenumber" pn="factivenumber" cn="活动方案编码" lock="-1" lix="500" />

        <input group="基本信息" el="116" ek="fbillhead" visible="1150" id="frenewalflag" fn="frenewalflag" pn="frenewalflag" cn="焕新订单标记" lock="-1" copy="0" notrace="false" ts="" defval="false" />
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fmembershiptranid" fn="fmembershiptranid" pn="fmembershiptranid" cn="商城交易流水号" copy="0" lock="-1" len="50" />
        <input group="基本信息" el="112" ek="fbillhead" visible="1150" id="fmembershippaydate" fn="fmembershippaydate" pn="fmembershippaydate" cn="商城支付日期" format="yyyy-MM-dd" xlsin="0" copy="0" lock="-1" />
        <input group="基本信息" el="116" ek="fbillhead" visible="1150" id="fissubmithq" fn="fissubmithq" pn="fissubmithq" cn="提交总部" lock="-1" copy="0" apipn="issubmithq" />
        <input group="基本信息" el="116" ek="fbillhead" visible="1150" id="fiszbrefund" fn="fiszbrefund" pn="fiszbrefund" cn="总部已退款" lock="-1" copy="0" apipn="iszbrefund" />
        <input group="基本信息" el="112" ek="fbillhead" visible="1150" id="fdeliverydate" fn="fdeliverydate" pn="fdeliverydate" cn="合同交货日期" format="yyyy-MM-dd" copy="0" apipn="deliveryDate" lock="-1" />
        <select group="基本信息" el="122" ek="fbillhead" visible="1150" id="fcustomcategory" fn="fcustomcategory" pn="fcustomcategory" cn="焕新定制柜类别" cg="焕新定制柜类别" refid="bd_enum" dfld="fenumitem"></select>
        <input group="基本信息" el="100" ek="fbillhead" visible="1150" id="fsecondorderno" fn="fsecondorderno" pn="fsecondorderno" cn="二级合同编号" copy="0" lock="-1" />
        <input group="基本信息" el="106" ek="fbillhead" visible="1150" id="frenewtype" fn="frenewtype" pn="frenewtype" cn="焕新订单类型" refid="ydj_renewtype" apipn="renewtype" copy="0" lock="-1" />
        <input group="基本信息" el="135" ek="fbillhead" visible="1150" id="forderimage" fn="forderimage" pn="forderimage" cn="合同附件" lock="-1" copy="0" />


        <input group="基本信息" el="116" ek="fbillhead" visible="1150" id="frenewalrectag" fn="frenewalrectag" pn="frenewalrectag" cn="焕新预收标记" lock="-1" copy="0" notrace="false" ts="" defval="false" />
        <input group="成本核算" el="105" ek="fbillhead" visible="1150" id="frecdealamount_gb" fn="frecdealamount_gb" pn="frecdealamount_gb" cn="国补预收成交金额" copy="0" lock="-1" notrace="false" />
        <input group="成本核算" el="105" ek="fbillhead" visible="1150" id="frecsumamount_gb" fn="frecsumamount_gb" pn="frecsumamount_gb" cn="国补预收总补贴金额" copy="0" lock="-1" notrace="false" />

        <!-- 一件代发 -->
        <input group="基本信息" el="116" ek="fbillhead" id="fpiecesendtag" fn="fpiecesendtag" pn="fpiecesendtag" visible="1150" cn="一件代发标记" lix="500" notrace="false" ts="" defval="false" lock="-1" copy="0" />

        <input group="基本信息" el="106" ek="fbillhead" visible="-1" id="fstoreid" fn="fstoreid" pn="fstoreid" cn="门店名称" notrace="false" refid="bas_store" canchange="false" lix="25" defls="true" />
    </div>

    <!--商品明细-->
    <table id="fentity" el="52" pk="fentryid" tn="t_ydj_poorderentry" pn="fentity" cn="商品明细" kfks="fmaterialid">
        <tr>
            <th lix="20" group="基本信息" el="112" ek="fentity" visible="1150" id="fplandate" fn="fplandate" pn="fplandate" ts="" cn="预计交货日期" copy="0" width="105" canchange="true" lock="0" />
            <th lix="30" el="152" ek="fentity" visible="1150" id="fhqderchgstatus_e" fn="fhqderchgstatus" pn="fhqderchgstatus" cn="总部变更行状态" vals="'01':'提交至总部','02':'关闭','03':'驳回'" defval="" copy="0" lock="-1" />
            <th lix="30" el="100" ek="fentity" type="text" id="fhqderchgcancelreason" fn="fhqderchgcancelreason" cn="总部变更取消原因" visible="1150" lock="-1" />
            <th lix="150" ek="fentity" el="101" id="fseq_e" fn="fseq_e" pn="fseq_e" visible="0" cn="顺序" copy="0" width="200" lock="-1" notrace="false"></th>
            <th lix="200" el="107" ek="fentity" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="1150" cn="商品编码"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0" frozen="1"></th>
            <th lix="201" ek="fentity" el="106" id="fmaterialid" fn="fmaterialid" refid="ydj_product" multsel="true" sformid="" ts="" cn="商品" visible="1150" reflvt="1" width="160" copy="0"
                dfld="fspecifica,fvolume,fgrossload,fpacksize,fstockunitid,fselectioncategoryid,fselcategoryid,fbedpartflag,fcategoryid,fauxseriesid,fispresetprop,fpackqty,fseriesid,fseltypeid,fpackagtype,fmainorgid,fispartflag"
                filter="fendpurchase='0'" must="1" frozen="1">商品</th>
            <th lix="202" el="107" ek="fentity" id="fmtrlmodel" fn="fmtrlmodel" pn="fmtrlmodel" visible="1150" cn="规格型号"
                lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fspecifica" refvt="0"></th>
            <th lix="203" ek="fentity" el="132" id="fattrinfo" fn="fattrinfo" pn="fattrinfo" cn="辅助属性" ctlfk="fmaterialid" nstdfk="funstdtype" pricefk="fprice" width="140" lock="0" visible="1150"></th>
            <th el="100" ek="fentity" id="fattrinfo_first" cn="初始辅助属性" fn="fattrinfo_first" pn="fattrinfo_first" visible="1150" len="4000" width="200" lock="-1" />
            <th lix="204" ek="fentity" el="100" len="2000" id="fcustomdes_e" fn="fcustomdes_e" pn="fcustomdes_e" cn="定制说明" width="200" visible="1150" canchange="true"></th>
            <th lix="205" ek="fentity" el="103" id="fbizqty" fn="fbizqty" pn="fbizqty" cn="采购数量" ctlfk="fbizunitid" format="0,000.00" basqtyfk="fqty" width="80" visible="1150" canchange="true" notrace="false"></th>
            <th lix="206" ek="fentity" el="109" id="funitid" fn="funitid" pn="funitid" cn="基本单位" refid="ydj_unit" sformid="" ctlfk="fmaterialid" width="80" lock="-1" visible="1150" copy="0" must="1" notrace="false"></th>
            <th lix="209" ek="fentity" el="104" id="fprice" fn="fprice" ts="" cn="采购单价" visible="1150" width="100" copy="0" canchange="true" format="0,000.000000" dformat="0,000.00" notrace="false"></th>
            <th lix="210" ek="fentity" el="105" id="famount" fn="famount" ts="" cn="金额" visible="1150" width="100" lock="-1" copy="0" canchange="true" notrace="false"></th>
            <th lix="211" el="102" ek="fentity" id="fdistrate" fn="fdistrate" pn="fdistrate" cn="折扣率" width="60" visible="1150" format="0,000.000000" apipn="distRate" canchange="true" notrace="false"></th>
            <th lix="212" el="104" ek="fentity" id="fdealprice" fn="fdealprice" pn="fdealprice" cn="成交单价" width="90" visible="1150" format="0,000.000000" dformat="0,000.00" apipn="dealPrice" canchange="true" notrace="false"></th>
            <th lix="213" el="105" ek="fentity" id="fdealamount_e" fn="fdealamount" pn="fdealamount" cn="成交金额" width="90" visible="1150" format="0,000.00" apipn="dealAmount" canchange="true" notrace="false"></th>
            <!--新增字段-->
            <th lix="227" el="104" ek="fentity" id="rounded_fdealprice" fn="rounded_fdealprice" pn="rounded_fdealprice" cn="保留2位小数成交单价" width="90" lock="-1" visible="1150" format="0,000.00" dformat="0,000.00" apipn="rounded_dealPrice"></th>
            <th lix="229" el="105" ek="fentity" id="rounded_fdealamount_e" fn="rounded_fdealamount" pn="rounded_fdealamount" cn="国补尾差" width="90" lock="-1" visible="1150" format="0,000.00" apipn="rounded_dealAmount"></th>


            <th lix="800" el="105" ek="fentity" id="fdistamount_e" fn="fdistamount" pn="fdistamount" cn="折扣额" width="90" visible="1086" format="0,000.00" apipn="distAmount" lock="-1" canchange="true" notrace="false"></th>

            <th lix="208" el="105" ek="fentity" id="fsubsidyamount" fn="fsubsidyamount" pn="fsubsidyamount" cn="补贴金额" width="90" visible="1150" format="0,000.00" apipn="subsidyAmount" canchange="true" notrace="false"></th>
            <th lix="209" el="104" ek="fentity" id="fhqretailprice" fn="fhqretailprice" pn="fhqretailprice" ts="" cn="总部零售价" width="90" visible="1150" format="0,000.000000" dformat="0,000.00" copy="0" lock="-1" canchange="true" notrace="false"></th>
            <th lix="209" el="104" ek="fentity" id="fretailprice" fn="fretailprice" pn="fretailprice" ts="" cn="零售价" width="90" visible="1150" format="0,000.000000" dformat="0,000.00" copy="0" canchange="true" notrace="false"></th>
            <th lix="210" el="105" ek="fentity" id="fretailamount" fn="fretailamount" pn="fretailamount" ts="" cn="零售金额" width="90" visible="1150" format="0,000.00" lock="-1" copy="0" canchange="true" notrace="false"></th>
            <th lix="211" el="105" ek="fentity" id="fretaildisprice" fn="fretaildisprice" pn="fretaildisprice" cn="零售折扣单价" width="90" visible="1150" format="0,000.00" apipn="retailDisPrice" lock="-1" notrace="false"></th>
            <th lix="211" el="105" ek="fentity" id="fretaildistamount" fn="fretaildistamount" pn="fretaildistamount" cn="零售折扣额" width="90" visible="1150" format="0,000.00" apipn="retailDistAmount" lock="-1" notrace="false"></th>
            <th lix="212" el="104" ek="fentity" id="fretaildealprice" fn="fretaildealprice" pn="fretaildealprice" cn="零售成交单价" width="100" visible="1150" format="0,000.000000" dformat="0,000.00" apipn="retailDealPrice" canchange="true" notrace="false"></th>
            <th lix="213" el="105" ek="fentity" id="fretaildealamount" fn="fretaildealamount" pn="fretaildealamount" cn="零售成交金额" width="100" visible="1150" format="0,000.00" apipn="retailDealAmount" canchange="true" notrace="false"></th>

            <th lix="800" ek="fentity" el="161" id="fmtrlimage" fn="fmtrlimage" pn="fmtrlimage" cn="图片" ctlfk="fmaterialid" width="200" visible="1086" lock="-1"></th>
            <th lix="230" el="107" ek="fentity" id="fbrandid" fn="fbrandid" cn="品牌" visible="1150" dispfk="fbrandid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>
            <th lix="235" el="107" ek="fentity" id="fseriesid" fn="fseriesid" cn="系列" visible="1150" dispfk="fseriesid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>
            <th lix="236" el="107" ek="fentity" id="fsubseriesid" fn="fsubseriesid" cn="子系列" visible="1086" dispfk="fsubseriesid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>
            <th lix="800" ek="fentity" el="100" id="factualspec" fn="factualspec" ts="" cn="实际规格" visible="1086" width="160" len="500"></th>
            <th lix="800" el="107" ek="fentity" id="fcustom" fn="fcustom" pn="fcustom" visible="1096" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fcustom" refvt="116"></th>
            <th lix="240" ek="fentity" el="109" id="fbizunitid" fn="fbizunitid" pn="fbizunitid" cn="采购单位" refid="ydj_unit" sformid="" ctlfk="fmaterialid" width="90" visible="1150" must="1" notrace="false"></th>
            <th lix="800" el="107" ek="fentity" visible="1096" id="fissuitflag" fn="fissuitflag" pn="fissuitflag" cn="是否套件" ctlfk="fmaterialid" dispfk="fsuiteflag" lock="-1" refValueType="116"></th>
            <th lix="800" el="106" ek="fentity" visible="1086" id="fsuitproductid" fn="fsuitproductid" pn="fsuitproductid" cn="套件商品" refid="ydj_product" lock="-1"></th>
            <th lix="245" el="100" ek="fentity" visible="1150" id="fsuitdescription" fn="fsuitdescription" pn="fsuitdescription" cn="套件说明" len="2000" lock="-1"></th>
            <th lix="800" el="100" ek="fentity" visible="1086" id="fsuitcombnumber" fn="fsuitcombnumber" pn="fsuitcombnumber" cn="套件组合号" lock="-1"></th>
            <th lix="800" el="100" ek="fentity" visible="1086" id="fpartscombnumber" fn="fpartscombnumber" pn="fpartscombnumber" cn="配件组合号" lock="-1"></th>
            <th lix="800" el="103" ek="fentity" id="fsubqty" fn="fsubqty" pn="fsubqty" visible="1086" cn="子件数量" width="120" lock="-1" copy="0" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th lix="800" el="100" ek="fentity" visible="1086" id="fparttype" fn="fparttype" pn="fparttype" cn="配件类型" len="100" lock="-1"></th>
            <th lix="280" ek="fentity" el="100" id="fnote" fn="fnote" pn="fnote" cn="非生产备注" visible="1150" width="160" len="1000" copy="0" canchange="true"></th>

            <th lix="280" ek="fentity" el="100" id="ffactorybillname" fn="ffactorybillname" pn="ffactorybillname" cn="订单名称" visible="-1"></th>

            <th lix="800" ek="fentity" el="107" id="fattribute" fn="fattribute" pn="fattribute" visible="0" cn="属性" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fattribute" refvt="0"></th>
            <th lix="800" el="135" ek="fentity" id="fmulfile" fn="fmulfile" pn="fmulfile" adld="true" maxCount="0" cn="附件" width="240" visible="1086"></th>
            <th lix="800" ek="fentity" el="100" len="2000" id="fmulfile_source" fn="fmulfile_source" pn="fmulfile_source" cn="上传方" visible="1086"></th>
            <!-- <th lix="109" el="107" ek="fentity" id="fcustom" fn="fcustom" pn="fcustom" visible="1150" cn="允许定制" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fcustom" refvt="116"></th>-->
            <!-- <th lix="109" el="107" ek="fentity" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="1150" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fispresetprop" refvt="116"></th>-->
            <th lix="800" el="107" ek="fentity" id="fispresetprop" fn="fispresetprop" pn="fispresetprop" visible="0" cn="允许选配" width="80" align="center" lock="-1" copy="1" notrace="true" ts="" ctlfk="fmaterialid" dispfk="fispresetprop" refvt="116"></th>
            <th lix="800" ek="fentity" el="103" id="fqty" fn="fqty" ts="" cn="基本单位数量" ctlfk="funitid" format="0,000.00" width="100" visible="1086" copy="0" canchange="true" notrace="false" lock="-1"></th>
            <th lix="800" el="103" ek="fentity" id="fpartqty" fn="fpartqty" pn="fpartqty" cn="配件数量" ctlfk="funitid" format="0,000.00" width="100" visible="0" apipn="qty" canchange="true"></th>
            <th lix="800" ek="fentity" el="104" id="fsalprice" fn="fsalprice" ts="" cn="销售单价" visible="1086" width="100" copy="0" lock="0" format="0,000.000000" dformat="0,000.00"></th>
            <th lix="800" el="100" ek="fentity" id="fmtono" fn="fmtono" pn="fmtono" cn="物流跟踪号" width="100" visible="1086" lock="0" canchange="true"></th>
            <th lix="800" el="149" ek="fentity" id="fownertype" fn="fownertype" pn="fownertype" dataviewname="v_bd_ownerdata" cn="货主类型" width="100" visible="1086" lock="0">
                <dataSourceDesc formId="ydj_customer" filter="" caption="客户"></dataSourceDesc>
                <dataSourceDesc formId="ydj_supplier" filter="" caption="供应商"></dataSourceDesc>
                <dataSourceDesc formId="ydj_dept" filter="" caption="部门"></dataSourceDesc>
            </th>
            <th lix="800" el="150" ek="fentity" id="fownerid" fn="fownerid" pn="fownerid" ctlfk="fownertype" cn="货主" width="100" visible="1086" lock="0"></th>
            <th lix="800" el="152" ek="fentity" visible="1086" id="fcooeditstatus" fn="fcooeditstatus" pn="fcooeditstatus" cn="供方处理状态" lock="-1"
                vals="'0':'正常','1':'修改','2':'新增','3':'删除'" defval="'0'"></th>
            <th lix="800" el="112" ek="fentity" id="fdemanddate" fn="fdemanddate" pn="fdemanddate" visible="1086" cn="需求日期" width="100"></th>
            <th lix="800" el="103" ek="fentity" id="freceiptqty" fn="freceiptqty" pn="freceiptqty" visible="1086" cn="基本单位收货数量" width="120"
                lock="-1" copy="0" notrace="false" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th lix="216" el="103" ek="fentity" id="finstockqty" fn="finstockqty" pn="finstockqty" visible="1150" cn="基本单位入库数量" width="120"
                lock="-1" copy="0" notrace="false" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th lix="800" el="103" ek="fentity" id="freturnnoticeqty" fn="freturnnoticeqty" pn="freturnnoticeqty" visible="1086" cn="基本单位退货通知数量" width="150"
                lock="-1" copy="0" notrace="false" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th lix="800" el="103" ek="fentity" id="freturnqty" fn="freturnqty" pn="freturnqty" visible="1086" cn="基本单位退换数量" width="120"
                lock="-1" copy="0" notrace="false" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th lix="800" el="103" ek="fentity" id="frefundqty" fn="frefundqty" pn="frefundqty" visible="1086" cn="基本单位退款数量" width="120"
                lock="-1" copy="0" notrace="false" ts="" ctlfk="funitid" roundType="0" format="0,000.00"></th>
            <th lix="800" el="103" ek="fentity" id="fbizreceiptqty" fn="fbizreceiptqty" pn="fbizreceiptqty" visible="1086" cn="采购收货数量" width="100"
                lock="-1" copy="0" notrace="false" ts="" ctlfk="fbizunitid" basqtyfk="freceiptqty" roundType="0" format="0,000.00"></th>
            <th lix="800" el="103" ek="fentity" id="fbizinstockqty" fn="fbizinstockqty" pn="fbizinstockqty" visible="1086" cn="采购入库数量" width="100"
                lock="-1" copy="0" notrace="false" ts="" ctlfk="fbizunitid" basqtyfk="finstockqty" roundType="0" format="0,000.00"></th>
            <th lix="800" el="103" ek="fentity" id="fbizreturnnoticeqty" fn="fbizreturnnoticeqty" pn="fbizreturnnoticeqty" visible="1086" cn="采购退货通知数量"
                lock="-1" copy="0" notrace="false" ts="" ctlfk="fbizunitid" basqtyfk="freturnnoticeqty" roundType="0" format="0,000.00" width="120"></th>
            <th lix="800" el="103" ek="fentity" id="fbizreturnqty" fn="fbizreturnqty" pn="fbizreturnqty" visible="1086" cn="采购退换数量" width="100"
                lock="-1" copy="0" notrace="false" ts="" ctlfk="fbizunitid" basqtyfk="freturnqty" roundType="0" format="0,000.00"></th>
            <th lix="800" el="103" ek="fentity" id="fbizrefundqty" fn="fbizrefundqty" pn="fbizrefundqty" visible="1086" cn="采购退款数量" width="100"
                lock="-1" copy="0" notrace="false" ts="" ctlfk="fbizunitid" basqtyfk="frefundqty" roundType="0" format="0,000.00"></th>
            <th lix="405" el="103" ek="fentity" id="fbizhqdeliveryqty" fn="fbizhqdeliveryqty" pn="fbizhqdeliveryqty" cn="总部已发货数" visible="36" width="100" lock="-1" copy="0"></th>
            <th lix="800" el="105" ek="fentity" id="ffee" fn="ffee" ts="" visible="1086" cn="费用" lock="-1" copy="0"></th>
            <th lix="800" el="103" ek="fentity" id="flinkqty" fn="flinkqty" ts="" visible="1086" cn="关联数量" lock="-1" copy="0"></th>
            <th lix="800" el="100" ek="fentity" id="fsuppliernumber" fn="fsuppliernumber" ts="" visible="1086" cn="供应商货号" lock="0" copy="0"></th>
            <th lix="800" el="160" ek="fentity" id="fclosestatus_e" fn="fclosestatus_e" pn="fclosestatus_e" visible="1086" cn="行关闭状态" defval="'0'" xlsin="0" copy="0" lock="-1"></th>
            <th lix="800" el="100" ek="fentity" id="fstaffname" fn="fstaffname" ts="" visible="1086" cn="导购员" lock="0" copy="0"></th>
            <th lix="800" ek="fentity" el="100" id="fsourceentryid_e" fn="fsourceentryid" pn="fsourceentryid_e" cn="来源单分录内码" visible="170" lock="-1" copy="0"></th>
            <th lix="800" el="140" ek="fentity" id="fsourceformid_e" fn="fsourceformid" ts="" cn="来源单类型" visible="1086" copy="0" lock="-1"></th>
            <th lix="800" el="141" ek="fentity" id="fsourcebillno_e" fn="fsourcebillno" ts="" cn="来源单编号" ctlfk="fsourceformid_e" visible="1086" copy="0" lock="-1"></th>
            <th lix="800" el="100" ek="fentity" id="fsourceinterid_e" fn="fsourceinterid" ts="" cn="来源单内码" visible="0" copy="0" lock="-1"></th>
            <th lix="800" el="108" ek="fentity" id="fsoorderno" fn="fsoorderno" pn="fsoorderno" visible="1086" cn="销售合同编号"
                lock="-1" copy="0" notrace="false" ts=""></th>
            <th lix="800" el="108" ek="fentity" id="fsoorderinterid" fn="fsoorderinterid" pn="fsoorderinterid" visible="0" cn="销售合同内码"
                lock="-1" copy="0" notrace="false" ts=""></th>
            <th lix="800" el="108" ek="fentity" id="fsoorderentryid" fn="fsoorderentryid" pn="fsoorderentryid" visible="0" cn="销售合同分录内码"
                lock="-1" copy="0" notrace="false" ts=""></th>
            <th lix="800" el="106" ek="fentity" visible="1150" id="fchannel" fn="fchannel" pn="fchannel" cn="合作渠道" refid="ste_channel" />
            <th lix="800" el="122" ek="fentity" visible="1150" id="fgoodschanneltype" fn="fgoodschanneltype" pn="fgoodschanneltype" cn="渠道类型" cg="渠道类型" refid="bd_enum" dfld="fenumitem"></th>
            <!--慕思扩展-->
            <th lix="208" el="116" ek="fentity" visible="1150" id="funstdtype" fn="funstdtype" pn="funstdtype" cn="是否非标" ctlfk="fmaterialid" dispfk="funstdtype" width="90" copy="0" lock="0" canchange="true" refValueType="116"></th>
            <th lix="214" el="152" ek="fentity" visible="1150" id="funstdtypestatus" fn="funstdtypestatus" pn="funstdtypestatus" cn="非标审批状态" vals="'01':'新建','02':'待审批','03':'审批通过','04':'驳回','05':'待定价','06':'终审'" defval="" copy="0" lock="-1"></th>
            <th lix="215" el="100" ek="fentity" visible="1150" id="funstdtypecomment" fn="funstdtypecomment" pn="funstdtypecomment" cn="非标审批意见" width="160" len="2000" copy="0" lock="-1"></th>

            <th lix="800" el="107" ek="fentity" visible="0" id="fselcategoryid" fn="fselcategoryid" pn="fselcategoryid" cn="选配类别" ctlfk="fmaterialid" dispfk="fselcategoryid" lock="-1"></th>
            <th lix="800" el="107" ek="fentity" visible="1086" id="fcategoryid" fn="fcategoryid" pn="fcategoryid" cn="商品类别" ctlfk="fmaterialid" dispfk="fcategoryid" lock="-1"></th>
            <th lix="800" el="107" ek="fentity" visible="0" id="fbedpartflag" fn="fbedpartflag" pn="fbedpartflag" cn="是否可添加配件" ctlfk="fmaterialid" dispfk="fbedpartflag" lock="-1"></th>
            <th lix="207" el="106" ek="fentity" id="fresultbrandid" fn="fresultbrandid" pn="fresultbrandid" cn="业绩品牌" refid="ydj_series" visible="1150"></th>
            <th lix="207" el="106" ek="fentity" id="fentrystaffid" fn="fentrystaffid" pn="fentrystaffid" cn="销售员" refid="ydj_staff" visible="1150"></th>

            <th lix="800" el="100" ek="fentity" visible="1086" id="fparttype" fn="fparttype" pn="fparttype" cn="配件类型" len="100" lock="-1"></th>
            <th lix="250" el="100" ek="fentity" visible="1150" id="fsofacombnumber" fn="fsofacombnumber" pn="fsofacombnumber" cn="沙发组合号" lock="-1"></th>
            <!--用于区分商品和其带出的配件-->
            <th lix="800" el="116" ek="fentity" visible="1086" id="fiscombmain" fn="fiscombmain" pn="fiscombmain" cn="配件主商品" lock="-1"></th>
            <!--用于区分标准、非标定制 自动带出的配件 和 手动添加的配件商品-->
            <th lix="800" el="116" ek="fentity" visible="1086" id="fisautopartflag" fn="fisautopartflag" pn="fisautopartflag" cn="是否自动带出配件" lock="-1"></th>
            <th lix="800" el="107" ek="fentity" visible="1086" id="fseltypeid" fn="fseltypeid" pn="fseltypeid" cn="型号" ctlfk="fmaterialid" dispfk="fseltypeid" lock="-1"></th>


            <th lix="255" el="100" ek="fentity" visible="1150" id="fcombine" fn="fcombine" pn="fcombine" cn="组合号" lock="-1"></th>
            <th lix="217" el="106" ek="fentity" visible="1150" id="fsaledeptid" fn="fsaledeptid" pn="fsaledeptid" cn="销售部门" refid="ydj_dept" lock="-1"></th>
            <th lix="220" el="106" ek="fentity" visible="1150" id="fcustomer" fn="fcustomer" pn="fcustomer" cn="客户" refid="ydj_customer" lock="-1"></th>
            <th lix="225" el="100" ek="fentity" visible="1150" id="fphone_e" fn="fphone" pn="fphone" cn="手机号" lock="-1"></th>
            <th lix="260" el="122" ek="fentity" visible="1150" id="fprodrequirement" fn="fprodrequirement" pn="fprodrequirement" refId="bd_enum" dfld="fenumitem" ts="" cg="生产要求" cn="生产要求"></th>
            <th lix="265" el="122" ek="fentity" visible="1150" id="fselsuiterequire" fn="fselsuiterequire" pn="fselsuiterequire" refId="bd_enum" dfld="fenumitem" ts="" cg="家纺套件要求" cn="家纺套件要求"></th>
            <!--套件相关-->
            <th lix="800" el="107" ek="fentity" visible="0" id="fissuit" fn="fissuit" pn="fissuit" cn="是否属于套件" lock="-1" ctlfk="fmaterialid" dispfk="fissuit" refValueType="116"></th>
            <!--<th lix="800" el="100" ek="fentity" visible="-1" id="fsuiteflag" fn="fsuiteflag" pn="fsuiteflag" cn="是否套件头" lock="-1" refid="ydj_product" ctlfk="fmaterialid" dispfk="fsuiteflag"></th>-->
            <th lix="800" el="100" ek="fentity" visible="0" id="fselectionnumber" fn="fselectionnumber" pn="fselectionnumber" cn="选配码" lock="-1"></th>
            <th lix="800" el="106" ek="fentity" visible="0" id="fforproductid" fn="fforproductid" pn="fforproductid" cn="所属套件" refid="ydj_product" lock="-1"></th>
            <th lix="800" el="106" ek="fentity" visible="0" id="fforsuiteselectionid" fn="fforsuiteselectionid" pn="fforsuiteselectionid" cn="所属套件选配码" refid="mt_suiteselection" lock="-1"></th>
            <th lix="800" el="100" ek="fentity" visible="0" id="fdescription_suite" fn="fdescription" pn="fdescription" cn="组合说明" len="2000" lock="-1"></th>
            <th lix="800" el="100" ek="fentity" visible="0" id="fpackagedescription" fn="fpackagedescription" pn="fpackagedescription" cn="套件组合说明" len="2000" lock="-1"></th>
            <th lix="800" el="107" ek="fentity" visible="0" id="fpackqty" fn="fpackqty" pn="fpackqty" cn="采购件数" lock="-1" ctlfk="fmaterialid" dispfk="fpackqty"></th>
            <th lix="800" el="106" ek="fentity" id="fshipperdeliver" fn="fshipperdeliver" pn="fshipperdeliver" cn="送达方" refid="bas_deliver" lock="-1" visible="0"></th>
            <th lix="800" el="107" ek="fentity" id="fauxseriesid" fn="fauxseriesid" cn="附属品牌" visible="-1" dispfk="fauxseriesid" ctlfk="fmaterialid" sformid="" width="100" lock="-1"></th>

            <th lix="800" el="103" ek="fentity" id="finstockpackqty" fn="finstockpackqty" ts="" visible="1150" cn="采购入库包数" lock="-1" copy="0"></th>
            <th lix="800" el="103" ek="fentity" id="fchangestockpackqty" fn="fchangestockpackqty" ts="" visible="1150" cn="采购退换包数" lock="-1" copy="0"></th>

            <!--总部合同扩展-->
            <th lix="800" ek="fentity" el="105" id="ftaxprice" fn="ftaxprice" cn="含税出厂价" visible="1150" width="100" copy="0" lock="-1"></th>
            <th lix="800" ek="fentity" el="105" id="fstardiscount" fn="fstardiscount" cn="星级折扣/上市折扣" visible="1150" width="100" copy="0" lock="-1"></th>
            <th lix="800" ek="fentity" el="105" id="fdepthdiscount" fn="fdepthdiscount" cn="深度护理折扣" visible="1150" width="100" copy="0" lock="-1"></th>
            <th lix="800" ek="fentity" el="105" id="fnewdiscount" fn="fnewdiscount" cn="新品折扣" visible="1150" width="100" copy="0" lock="-1"></th>
            <th lix="800" ek="fentity" el="105" id="fexpenserebate" fn="fexpenserebate" cn="费用支持返利" visible="1150" width="100" copy="0" lock="-1"></th>
            <th lix="800" ek="fentity" el="105" id="fotherdiscount" fn="fotherdiscount" cn="其他折扣" visible="1150" width="100" copy="0" lock="-1"></th>
            <th lix="800" ek="fentity" el="105" id="fsapdiscount" fn="fsapdiscount" cn="SAP折扣总额" visible="1150" width="100" copy="0" lock="-1"></th>

            <!--总部促销活动-->
            <th lix="800" el="100" ek="fentity" visible="1150" id="fpromotion" fn="fpromotion" pn="fpromotion" cn="促销活动" lock="-1" canchange="true"></th>
            <th lix="800" el="100" ek="fentity" visible="1150" id="fcombinenumber" fn="fcombinenumber" pn="fcombinenumber" cn="组合促销编号" lock="-1" canchange="true" notrace="false"></th>
            <th lix="800" el="100" ek="fentity" visible="1150" id="fcombineremark" fn="fcombineremark" pn="fcombineremark" cn="组合促销描述" lock="-1" canchange="true" notrace="false"></th>
            <th lix="800" el="103" ek="fentity" visible="1150" id="fcombineqty" fn="fcombineqty" pn="fcombineqty" cn="套餐组合基数" lock="-1" canchange="true" notrace="false"></th>
            <th lix="800" el="103" ek="fentity" visible="1150" id="fcombinerate" fn="fcombinerate" pn="fcombinerate" cn="套餐折扣率%" lock="-1" canchange="true"></th>
            <th lix="800" el="103" ek="fentity" visible="0" id="fgroupnumber" fn="fgroupnumber" pn="fgroupnumber" cn="子组号" lock="-1" canchange="true" notrace="false"></th>
            <th lix="800" el="102" ek="fentity" visible="1150" id="fsumpushreceiveqty" fn="fsumpushreceiveqty" pn="fsumpushreceiveqty" cn="累计下推收货作业数量" lock="-1" copy="0" />

            <th el="100" ek="fentity" id="fomsbillno" fn="fomsbillno" pn="fomsbillno" visible="1150" cn="定制OMS单号(后台字段)" copy="0" lock="-1"></th>
            <th lix="281" el="116" ek="fentity" id="furgent" fn="furgent" pn="furgent" cn="加急" visible="1086"></th>
            <th lix="282" el="100" ek="fentity" id="fadditionalcharge" fn="fadditionalcharge" pn="fadditionalcharge" cn="附加费" visible="1086" lock="-1">></th>

            <th el="116" ek="fentity" visible="0" id="fissyncai" fn="fissyncai" pn="fissyncai" cn="是否已发送AI云" copy="0" lock="0"></th>

            <th lix="227" el="104" ek="fentity" id="fsubrecdealamount_gb" fn="fsubrecdealamount_gb" pn="fsubrecdealamount_gb" cn="明细国补预收成交金额" width="90" lock="-1" visible="1150" format="0,000.000000" dformat="0,000.00" canchange="true" notrace="false"></th>
            <th lix="227" el="104" ek="fentity" id="fsubrecsumamount_gb" fn="fsubrecsumamount_gb" pn="fsubrecsumamount_gb" cn="明细国补预收补贴金额" width="90" lock="-1" visible="1150" format="0,000.000000" dformat="0,000.00" canchange="true" notrace="false"></th>
            <th lix="227" ek="fentity" el="101" id="fsubrenewseq" fn="fsubrenewseq" pn="fsubrenewseq" visible="1150" cn="焕新预售单行号" copy="0" width="200" lock="-1" notrace="false"></th>
            <!--库存水位线-->
            <th el="152" ek="fentity" visible="32" id="fstockwaterline" fn="fstockwaterline" pn="fstockwaterline" cn="库存水位线状态"
                vals="'0':'','1':'红灯','2':'绿灯','3':'黄灯'" xlsin="0" copy="0" lix="60" align="center" lock="-1"></th>
        </tr>
    </table>

    <!--商品子明细，由“销售订单=>拆单完成”后协同过来，整个明细都是只读状态，不允许修改-->
    <table id="fdetail" el="53" pek="fentity" pk="fdetailid" tn="t_ydj_purchasedetail" pn="fdetail" cn="商品子明细">
        <tr>
            <th el="100" ek="fdetail" id="fproductid_sub" fn="fproductid" pn="fproductid" cn="商品" width="160" lock="-1" visible="0" copy="0"></th>
            <th el="100" ek="fdetail" id="funitid_sub" fn="funitid" pn="funitid" cn="单位" width="65" lock="-1" visible="0" copy="0"></th>
            <th el="100" ek="fdetail" id="fattrinfo_sub" fn="fattrinfo" pn="fattrinfo" cn="辅助属性" width="140" lock="-1" visible="0" copy="0" len="500"></th>
            <th el="133" ek="fdetail" id="flength_sub" fn="flength" pn="flength" cn="长" width="65" lock="-1" visible="0" copy="0"></th>
            <th el="133" ek="fdetail" id="fwidth_sub" fn="fwidth" pn="fwidth" cn="宽" width="65" lock="-1" visible="0" copy="0"></th>
            <th el="133" ek="fdetail" id="fthick_sub" fn="fthick" pn="fthick" cn="厚" width="65" lock="-1" visible="0" copy="0"></th>
            <th el="103" ek="fdetail" id="fqty_sub" fn="fqty" pn="fqty" cn="数量" width="65" lock="-1" visible="0" copy="0"></th>
            <th el="104" ek="fdetail" id="fprice_sub" fn="fprice" pn="fprice" cn="单价" width="80" lock="-1" visible="0" format="0,000.000000" dformat="0,000.00" copy="0"></th>
            <th el="105" ek="fdetail" id="famount_sub" fn="famount" pn="famount" cn="金额" width="80" lock="-1" visible="0" format="0,000.00" copy="0"></th>
            <th el="100" ek="fdetail" id="fdescription_sub" fn="fdescription" pn="fdescription" cn="非生产备注" width="120" lock="-1" visible="0" copy="0"></th>
            <th el="100" ek="fdetail" id="fsourcedetailid_sub" fn="fsourcedetailid" cn="源单明细ID" visible="0" lock="-1" copy="0"></th>
        </tr>
    </table>

    <!--图纸明细-->
    <table id="fdrawentity" el="52" pk="fdrawentryid" tn="t_ydj_podrawentry" pn="FDrawEntity" kfks="ffileid" cn="图纸明细">
        <tr>
            <th ek="fdrawentity" el="100" id="ffilename" fn="ffilename" pn="ffilename" cn="文件名" visible="1150" width="350" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="110" id="ffileid" fn="ffileid" pn="ffileid" cn="文件id" visible="0" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="100" id="ffileformat" fn="ffileformat" pn="ffileformat" cn="文件格式" visible="1150" width="70" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="100" id="ffilesize" fn="ffilesize" pn="ffilesize" cn="文件大小" visible="1150" width="70" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="100" id="fnote_d" fn="fnote" pn="fnote" cn="备注" visible="1124" width="260" copy="0"></th>
            <th ek="fdrawentity" el="100" id="fuploader" fn="fuploader" pn="fuploader" cn="上传人" visible="1150" width="65" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="100" id="fuploaderid" fn="fuploaderid" pn="fuploaderid" cn="上传人id" visible="0" width="100" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="113" id="fuptime" fn="fuptime" pn="fuptime" cn="上传时间" visible="1150" width="120" lock="-1" copy="0"></th>
            <th ek="fdrawentity" el="100" id="fsourceentryid" fn="fsourceentryid" cn="源单明细ID" visible="0" lock="-1" copy="0" width="120"></th>
            <th ek="fdrawentity" el="144" id="foplist" fn="foplist" pn="foplist" cn="操作" btnid="download,delete" width="120" btntxt="下载,删除" visible="1150" lock="-1" align="left"></th>
        </tr>
    </table>

    <!--业务进度-->
    <table id="fndentry" el="56" pk="fndentryid" tn="t_ydj_ponodedefine" cn="业务进度" apipn="ndEntry" kfks="fnodename">
        <tr>
            <th lix="10" ek="fndentry" el="100" id="fnodename" fn="fnodename" pn="fnodename" cn="节点名称" visible="1150" lix="100" copy="0" width="200" lock="-1">节点名称</th>
            <th lix="10" ek="fndentry" el="100" id="fnodecontent" fn="fnodecontent" pn="fnodecontent" cn="节点内容" visible="1150" copy="0" width="200" lock="-1">节点内容</th>
            <th lix="20" ek="fndentry" el="152" id="fnodetype" fn="fnodetype" pn="fnodetype" visible="1150" cn="节点类型" copy="0" lock="-1" notrace="false" ts="" vals="1:'业务节点',2:'人工节点',3:'协同节点'" defval="'1'"></th>
            <th lix="20" ek="fndentry" el="101" id="fnodeindex" fn="fnodeindex" pn="fnodeindex" visible="1150" cn="节点顺序" copy="0" width="200" lock="-1">节点顺序</th>
            <th lix="20" ek="fndentry" el="152" id="fnodestatus" fn="fnodestatus" pn="fnodestatus" visible="1150" cn="节点状态" copy="0" lock="-1" notrace="false" ts="" vals="'0':'计划','1':'完成'"></th>
            <th lix="40" ek="fndentry" el="106" id="freponseuserid" fn="freponseuserid" pn="freponseuserid" cn="操作人id" refId="Sec_User" visible="0" width="100" lock="-1" copy="0" apipn="operatorId"></th>
            <th lix="50" ek="fndentry" el="113" id="ffinishtime" fn="ffinishtime" pn="ffinishtime" cn="完成时间" visible="96" width="170" lock="-1" copy="0" apipn="operatorTime"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="submitsynergy" op="submitsynergy" opn="发送" data="" permid="pur_submitsynergy" ubl="1">
            <li el="11" vid="510" cn="审核之后才能发送" data="{'expr':'fstatus==\'E\' ','message':'审核后才能发送'}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="repealsynergy" op="repealsynergy" opn="撤销发送" data="" permid="pur_repealsynergy" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="pricefirm" op="pricefirm" opn="确认" data="" permid="pur_pricefirm" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="cancelconfirm" op="cancelconfirm" opn="取消确认" data="" permid="pur_cancelconfirm" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="push2receiptnotice" op="push" opn="收货" data="{'parameter':{'ruleId':'ydj_purchaseorder2pur_receiptnotice'}}" permid=""></ul>
        <ul el="10" ek="fbillhead" id="push2poreturn" op="push" opn="退货" data="{'parameter':{'ruleId':'stk_postockin2stk_postockreturn'}}" permid=""></ul>
        <ul el="10" ek="fbillhead" id="confirmreceiv" op="confirmreceiv" opn="确认收货" data="" permid="pur_confirmreceiv" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="cancelreceiv" op="cancelreceiv" opn="取消收货" data="" permid="pur_cancelreceiv" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="payment" op="payment" opn="付款" data="" permid="pur_payment" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="push2poinstock" op="push" opn="入库" data="{'parameter':{'ruleId':'ydj_purchaseorder2stk_postockin'}}" permid=""></ul>
        <ul el="10" ek="fbillhead" id="push2recscantask" op="push" opn="生成收货任务" data="{'parameter':{'ruleId':'ydj_purchaseorder2bcm_receptionscantask'}}" permid="" ubl="1"></ul>
        <ul el="10" ek="fbillhead" id="createrecscantask" op="createrecscantask" opn="生成收货任务" permid="" ubl="1"></ul>

        <ul el="10" id="save" op="save" opn="保存">
            <!--<li el="11" vid="510" cn="采购部门不能为空" data="{'expr':'fpodeptid!=\'\' and fpodeptid!=\' \'','message':'采购部门不能为空！'}"></li>-->
            <!--二级分销的采购变更申请单新的采购数量变成0，那这时候的审核状态其实是已审核，如果不加上这个，等一级的销售合同变更申请单审核的时候，将数量修改到采购订单，这时候就会被校验中，那么这时候就不能校验了，所以就加了审核状态不校验-->
            <li el="11" vid="510" ek="fentity" cn="基本单位采购数量必须大于0" data="{'expr':'fqty>0 or fbilltypeid==\'ydj_purchaseorder_zb\' or fcooeditstatus==\'3\' or fchangestatus !=\'0\' or fstatus == \'E\'','message':'基本单位采购数量必须大于0！'}"></li>
            <li el="11" vid="510" ek="fentity" cn="采购数量必须大于0" data="{'expr':'fbizqty>0 or fbilltypeid==\'ydj_purchaseorder_zb\'  or fcooeditstatus==\'3\' or fchangestatus !=\'0\' or fstatus == \'E\'','message':'采购数量必须大于0！'}"></li>
            <li el="11" vid="510" ek="fentity" cn="单价必须大于0" data="{'expr':'fprice>=0 or fcooeditstatus==\'3\'','message':'单价不允许为负数！'}"></li>
            <li el="11" vid="510" ek="fentity" cn="成交单价必须大于0" data="{'expr':'fdealprice>=0 or fcooeditstatus==\'3\'','message':'成交单价不允许为负数！'}"></li>
            <li el="11" vid="510" cn="成交金额不能小于已付金额" data="{'expr':'ffbillamount>=fsettleamount-factrefundamount','message':'成交金额不能小于已付金额！'}"></li>
            <li el="11" vid="510" ek="fentity" cn="货主字段不能为空!" data="{'expr':'(fownertype=\'\' and fownerid=\'\') or (fownertype!=\'\' and fownerid!=\'\')','message':'货主字段不能为空!'}"></li>
            <li el="11" vid="3007" cn="套件商品明细的套件组合号不能为空" data="{'productFieldKey':'fmaterialid','suitCombNumberFieldKey':'fsuitcombnumber'}"></li>
            <li el="11" vid="3008" cn="允许选配的商品明细的辅助属性不能为空" data="{'productFieldKey':'fmaterialid'}"></li>
            <li el="17" sid="1004" cn="变更生效执行"></li>
            <li el="11" vid="517" id="save_valid_sourcebill" cn="保存时校验源单明细行是否存在于源单中"
                data="{'sourceTypeFieldKey':'fsourceformid_e','sourceNoFieldKey':'fsourcebillno_e','sourceEntryIdFieldKey':'fsourceentryid_e'}" precon=""></li>
        </ul>
        <ul el="10" id="submit" op="submit" opn="提交">
            <!--【240186】 【慕思现场-正式问题-310】采购订单增加审核逻辑，如果单据类型为总部手工单，则不校验商品数量是否为0 需求：采购订单增加审核逻辑，如果单据类型为总部手工单，则不校验商品明细数量是否为0 -->
            <li el="11" vid="510" ek="fentity" cn="采购数量必须大于0" data="{'expr':'fbizqty>0 or fcooeditstatus==\'3\' or fchangestatus !=\'0\'','message':'采购数量必须大于0！'}"></li>
            <!-- <li el="11" vid="510" ek="fentity" cn="采购数量必须大于0" data="{'expr':'fbizqty>0 or fbilltypeid==\'ydj_purchaseorder_zb\'  or fcooeditstatus==\'3\' or fchangestatus !=\'0\'','message':'采购数量必须大于0！'}"></li> -->
            <!--<li el="11" vid="510" ek="fentity" cn="采购数量必须大于0" data="{'expr':'fbizqty>0 or fcooeditstatus==\'3\' or fchangestatus !=\'0\'','message':'对不起，不允许提交采购数量为0的商品！'}"></li>-->
            <!--<li el="11" vid="510" cn="采购部门不能为空" data="{'expr':'fpodeptid!=\'\' and fpodeptid!=\' \'','message':'采购部门不能为空！'}"></li>-->
        </ul>
        <ul el="10" id="audit" op="save" opn="保存">
            <!--<li el="11" vid="510" cn="采购部门不能为空" data="{'expr':'fpodeptid!=\'\' and fpodeptid!=\' \'','message':'采购部门不能为空！'}"></li>-->
            <!--<li el="17" sid="2004" cn="反写关联流程" data="{
            'executeType':'add'
            }"></li>-->
        </ul>
        <ul el="10" id="audit" op="audit" opn="审核">
            <!--<li el="11" vid="510" cn="采购部门不能为空" data="{'expr':'fpodeptid!=\'\' and fpodeptid!=\' \'','message':'采购部门不能为空！'}"></li>-->
            <li el="17" sid="1002" cn="反写采购申请单已订购数量" data="{
                'sourceFormId':'pur_reqorder',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid_e',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'forderqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'forderqty&gt;fqty',
                'excessMessage':'申请单已订购数量不允许超过需求数量！'
                }"></li>

            <li el="17" sid="1002" cn="反写销售合同采购数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'fpurqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
            <li el="17" sid="1004" cn="变更生效执行"></li>
            <!--<li el="17" sid="2004" cn="反写关联流程" data="{
            'executeType':'add'
            }"></li>-->
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="11" vid="511" cn="进行了入库后不可以反审核" data='{"expr": [{"linkFormId":"stk_postockin","linkFieldKey":"fpoorderinterid"},
                {"linkFormId":"pur_receiptnotice","linkFieldKey":"fpoorderinterid"}],"message":"已经生成了下游单据，不允许反审核！"}'></li>

            <li el="17" sid="1002" cn="反写采购申请单已订购数量" data="{
                'sourceFormId':'pur_reqorder',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid_e',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'forderqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'forderqty&gt;fqty',
                'excessMessage':'申请单已订购数量不允许超过需求数量！'
                }"></li>

            <li el="17" sid="1002" cn="反写销售合同采购数量" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'fpurqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'fpurqty&gt;fqty',
                'excessMessage':'已采购数据不允许大于数量！'
                }"></li>

            <!--<li el="17" sid="2004" cn="反写关联流程" data="{
            'executeType':'remove'
            }"></li>-->
        </ul>
        <ul el="10" ek="fbillhead" id="change" op="change" opn="变更">
            <li el="11" vid="510" cn="提交至总部不允许变更" data="{'expr':'fhqderstatus !=\'02\'','message':'总部合同状态等于提交至总部, 不允许变更 !'}"></li>
            <li el="17" sid="1002" cn="反写采购申请单已订购数量" data="{
                'sourceFormId':'pur_reqorder',
                'sourceControlFieldKey':'fqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsourceentryid_e',
                'linkFormFieldKey':'',
                'linkFilterString':'fstatus=\'E\'',
                'writebackFieldKey':'forderqty',
                'expression':'fqty',
                'writebackMode':0,
                'excessCondition':'forderqty&gt;fqty',
                'excessMessage':'申请单已订购数量不允许超过需求数量！'
                }"></li>

            <!--<li el="17" sid="1002" cn="反写销售合同采购数量" data="{
            'sourceFormId':'ydj_order',
            'sourceControlFieldKey':'fqty',
            'sourceLinkFieldKey':'',
            'linkIdFieldKey':'fsoorderentryid',
            'linkFormFieldKey':'',
            'linkFilterString':'fstatus=\'E\'',
            'writebackFieldKey':'fpurqty',
            'expression':'fqty',
            'writebackMode':0,
            'excessCondition':'fpurqty&gt;fqty',
            'excessMessage':'已采购数据不允许大于数量！'
            }"></li>-->
            <!--<li el="17" sid="2004" cn="反写关联流程" data="{
            'executeType':'remove'
            }"></li>-->
        </ul>

        <ul el="10" ek="fbillhead" id="unchange" op="unchange" opn="取消变更">
            <!--<li el="17" sid="1002" cn="反写销售合同采购数量" data="{
            'sourceFormId':'ydj_order',
            'sourceControlFieldKey':'fqty',
            'sourceLinkFieldKey':'',
            'linkIdFieldKey':'fsoorderentryid',
            'linkFormFieldKey':'',
            'linkFilterString':'fstatus=\'E\'',
            'writebackFieldKey':'fpurqty',
            'expression':'fqty',
            'writebackMode':0,
            'excessCondition':'fpurqty&gt;fqty',
            'excessMessage':'已采购数据不允许大于数量！'
            }"></li>-->

        </ul>
        <ul el="10" ek="fentity" id="bizclose" op="bizclose" opn="关闭"
            data="{'parameter':{'linkStatusFieldKey':'fclosestatus_e','wholeStatusFieldKey':'fclosestatus'}}">
            <li el="11" ek="fentity" vid="510" cn="检查是否允许关闭" data="{
                'expr':'(fstatus=\'E\' or fchangestatus =\'1\' or fbilltypeid =\'ydj_purchaseorder_zb\')  and fclosestatus_e!=\'3\' and fclosestatus_e!=\'4\'',
                'message':'当前单据必须是已审核 或 变更中，且当前行关闭状态不为自动关闭 或 手工关闭 ！'
                }" ubl="1"></li>
        </ul>
        <ul el="10" ek="fentity" id="queryinventory" op="queryinventory" opn="查询一级总部停产及自建商品库存"
            data="{
                'parameter':{
                    'fieldMaps':{
                        'fcustomdesc':'fcustomdes_e',
                        'fusableqty':'fqty'
                    }

                }
            }"></ul>
        <ul el="10" ek="fentity" id="unclose" op="unclose" opn="反关闭"
            data="{'parameter':{'linkStatusFieldKey':'fclosestatus_e','wholeStatusFieldKey':'fclosestatus'}}">
            <li el="11" ek="fentity" vid="510" cn="检查是否允许反关闭" data="{
                'expr':'(fstatus=\'E\' or fchangestatus =\'1\'  or fbilltypeid =\'ydj_purchaseorder_zb\') and fclosestatus_e=\'4\'',
                'message':'当前单据必须是已审核 或 变更中，且当前行关闭状态必须 手工关闭 ！'
                }" ubl="1"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="refund" op="refund" opn="退款" data="" permid="ydj_purchaseorder_refund" ubl="1"></ul>

        <ul el="10" id="submitagent" op="submitagent" opn="提交一级经销" data="" permid="ydj_submitagent" ubl="1"></ul>
        <ul el="10" id="unstdtypeaudit" op="unstdtypeaudit" opn="非标审批" data="" permid="" ubl="1"></ul>
        <ul el="10" id="tbSubmitHQ" op="submithq" opn="提交总部" data="" permid="ydj_purchaseorder_submithq" ubl="1"></ul>
        <ul el="10" id="tbReNewSubmitHQ" op="renewsubmithq" opn="焕新提交总部" data="" permid="ydj_purchaseorder_renewsubmithq" ubl="1"></ul>
        <ul el="10" id="tbSaveSubmit" op="save" opn="保存并提交" data="" permid=""></ul>
        <ul el="10" id="tbSaveAudit" op="save" opn="保存并审核" data="" permid=""></ul>
        <ul el="10" id="btnunbindcontract" op="unbindcontract" opn="解绑合同产品" data="" permid="pur_unbindcontract" ubl="1">
            <li el="11" id="3010_unbindcontract" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>

        <ul el="10" ek="fbillhead" id="audit" op="audit" opn="审核">
            <li el="11" id="3010_audit" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="unaudit" op="unaudit" opn="反审核">
            <li el="11" id="3010_unaudit" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
        </ul>
        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除">
            <li el="11" id="3010_delete" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>

            <li el="17" sid="1002" cn="反写销售合同已转采购数" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fbizqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'ftranspurqty',
                'expression':'fbizqty',
                'writebackMode':0,
                'excessCondition':'fcancelstatus=\'0\'',
                'excessMessage':''
                }"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="cancel" op="cancel" opn="作废">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="17" sid="1002" cn="反写销售合同已转采购数" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fbizqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'ftranspurqty',
                'expression':'fbizqty',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
        </ul>
        <ul el="10" id="uncancel" op="uncancel" opn="反作废">
            <li el="11" vid="3010" cn="校验源单变更状态是否是“变更中”或“变更已提交”，如果是则不允许执行该操作" data=""></li>
            <li el="17" sid="1002" cn="反写销售合同已转采购数" data="{
                'sourceFormId':'ydj_order',
                'sourceControlFieldKey':'fbizqty',
                'sourceLinkFieldKey':'',
                'linkIdFieldKey':'fsoorderentryid',
                'linkFormFieldKey':'',
                'linkFilterString':'',
                'writebackFieldKey':'ftranspurqty',
                'expression':'fbizqty',
                'writebackMode':0,
                'excessCondition':'',
                'excessMessage':''
                }"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="savesubmit" op="savesubmit" opn="保存并提交" data="" permid=""></ul>
        <ul el="10" ek="fbillhead" id="saveaudit" op="saveaudit" opn="保存并审核" data="" permid=""></ul>
        <ul el="10" ek="fbillhead" id="stockup" op="stockup" opn="定制柜备货" data="" permid="fw_stockup"></ul>
        <ul el="10" ek="fbillhead" id="swingfield" op="swingfield" opn="定制柜摆场" data="" permid="fw_swingfield"></ul>
        <ul el="10" ek="fbillhead" id="partslistquery" op="partslistquery" opn="部件清单查询" data="" permid="fw_partslistquery"></ul>
        <ul el="10" id="quodetails" op="quodetails" opn="报价明细" data="" permid="fw_quodetails"></ul>
        <!-- <ul el="10" ek="fbillhead" id="initiatechangeapply" op="initiatechangeapply" opn="发起变更申请" permid="fw_initiatechangeapply"></ul> -->
        <ul el="10" ek="fbillhead" id="initiatechangeapply" op="initiatechangeapply" opn="发起变更申请" permid="fw_initiatechangeapply"></ul>
        <ul el="10" ek="fbillhead" id="querychangeapply" op="querychangeapply" opn="变更申请记录" permid="fw_querychangeapply"></ul>
        <ul el="10" ek="fbillhead" id="pushfeedback" op="pushfeedback" opn="总部售后" permid="fw_pushfeedback"></ul>
    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="ydj_submitagent" cn="提交一级经销"></ul>
        <ul el="12" id="pur_submitsynergy" cn="发送"></ul>
        <ul el="12" id="pur_repealsynergy" cn="撤销发送"></ul>
        <ul el="12" id="pur_pricefirm" cn="确认"></ul>
        <ul el="12" id="pur_cancelconfirm" cn="取消确认"></ul>
        <ul el="12" id="pur_confirmreceiv" cn="确认收货"></ul>
        <ul el="12" id="pur_cancelreceiv" cn="取消收货"></ul>
        <ul el="12" id="pur_payment" cn="付款"></ul>
        <ul el="12" id="ydj_purchaseorder_refund" cn="退款"></ul>
        <ul el="12" id="ydj_purchaseorder_submithq" cn="提交总部"></ul>
        <ul el="12" id="ydj_purchaseorder_renewsubmithq" cn="焕新提交总部"></ul>
        <ul el="12" id="pur_unbindcontract" cn="解绑合同产品"></ul>
        <ul el="12" id="fw_savesubmit" cn="保存并提交"></ul>
        <ul el="12" id="fw_saveaudit" cn="保存并审核"></ul>
        <ul el="12" id="fw_stockup" cn="定制柜备货"></ul>
        <ul el="12" id="fw_swingfield" cn="定制柜摆场"></ul>

        <ul el="12" id="fw_change" cn="变更" order="16"></ul>
        <ul el="12" id="fw_submitchange" cn="提交变更" order="17"></ul>
        <ul el="12" id="fw_unchange" cn="取消变更" order="17"></ul>
        <ul el="12" id="fw_partslistquery" cn="部件清单查询"></ul>
        <ul el="12" id="fw_quodetails" cn="报价明细"></ul>
        <ul el="12" id="fw_initiatechangeapply" cn="发起变更申请"></ul>
        <ul el="12" id="fw_querychangeapply" cn="变更申请记录"></ul>
        <ul el="12" id="fw_pushfeedback" cn="总部售后"></ul>
    </div>

</body>
</html>

